name: flux_firebase
description: A Firebase package for FluxStore apps
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  fstore:
    path: ../../
  inspireui:
    path: ../inspireui/

  # FIREBASE PACKAGES
  firebase_core:
  firebase_analytics:
  firebase_auth: ^5.4.2
  firebase_remote_config:
  firebase_dynamic_links:
  cloud_firestore:

  # Additional Firebase packages. To prevent unexpected upgrade.
  firebase_core_platform_interface:
  firebase_analytics_platform_interface:
  firebase_auth_platform_interface:
  firebase_remote_config_platform_interface:
  cloud_firestore_platform_interface:
  firebase_messaging_platform_interface:

  # PUSH NOTIFICATION
  firebase_messaging:

  # OTHERS
  intl: ^0.20.0
  timeago: ^3.7.0
  #  flutter_local_notifications: ^18.0.1
  #  share_plus: ^10.1.2
  provider: ^6.1.2
  collection: any
  easy_debounce: ^2.0.3
  url_launcher_ios: ^6.3.2

dependency_overrides:
  url_launcher_ios: ^6.3.2
  timeago: ^3.7.0
  intl: ^0.20.0
  async: ^2.12.0
  dio_web_adapter: ^2.1.0
  youtube_player_iframe: ^5.2.1

flutter:
  uses-material-design: true