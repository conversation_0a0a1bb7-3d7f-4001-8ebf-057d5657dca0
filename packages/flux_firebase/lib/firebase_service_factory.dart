/// Service
import 'package:fstore/services/firebase/dynamic_link_service.dart';
import 'package:fstore/services/firebase/firebase_analytics_service.dart';
import 'firebase_auth_service.dart';
import 'firebase_remote_service.dart';

/// Implement service
import 'impl/dynamic_link_service_impl.dart';
import 'impl/firebase_analytics_service_impl.dart';
import 'impl/firebase_auth/firebase_auth_service_impl.dart';
import 'impl/firebase_remote_service_impl.dart';

class FirebaseServiceFactory {
  static T? create<T>() {
    switch (T) {
      case FirebaseAuthService _:
        return FirebaseAuthServiceImpl() as T;
      case FirebaseAnalyticsService _:
        return FirebaseAnalyticsServiceImpl() as T;
      case DynamicLinkService _:
        return DynamicLinkServiceImpl() as T;
      case FirebaseRemoteServices _:
        return FirebaseRemoteServicesImpl() as T;
      default:
        return null;
    }
  }
}
