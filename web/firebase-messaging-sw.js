
importScripts("https://www.gstatic.com/firebasejs/9.19.1/firebase-app-compat.js");
importScripts("https://www.gstatic.com/firebasejs/9.19.1/firebase-messaging-compat.js");

firebase.initializeApp({
 apiKey: "AIzaSyBynW2MzCY1fNEkpri4hiaXRqEU-j94OYk",
 authDomain: "idea2app-dev.firebaseapp.com",
 projectId: "idea2app-dev",
 storageBucket: "idea2app-dev.appspot.com",
 messagingSenderId: "163437334343",
 appId: "1:163437334343:web:8152fe124a60f0b7b98662",
 measurementId: "G-K3XP56P0NB"
});

const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage(async (payload) => {
  console.log('Received background message:', payload);

  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    icon: '/icons/Icon-192.png',
    badge: '/icons/Icon-192.png',
    data: payload.data,
    click_action: payload.notification.click_action,
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});