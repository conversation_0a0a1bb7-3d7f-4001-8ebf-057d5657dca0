<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="notiColor">#2eb0fe</color>
    <!-- Theme applied to the Android Window while the process is starting TODO-NotificationIconColor-->
    <style name="LaunchTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Show a splash screen on the activity. Automatically removed when
             <PERSON>lutter draws its first frame -->
        <item name="android:windowBackground">@drawable/launch_background</item>
        <item name="android:windowFullscreen">false</item>
    </style>
    <!-- Theme applied to the Android Window as soon as the process has started.
         This theme determines the color of the Android Window while your
         Flutter UI initializes, as well as behind your Flutter UI while its
         running.
         
         This Theme is only used starting with V2 of Flutter's Android embedding. -->
    <style name="NormalTheme" parent="Theme.MaterialComponents">
        <item name="android:windowBackground">@android:color/white</item>
    </style>
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar" />
</resources>