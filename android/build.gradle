//buildscript {
//    ext.kotlin_version = '1.8.0'
//    repositories {
//        google()
//        mavenCentral()
//    }
//
//    dependencies {
//        classpath 'com.google.gms:google-services:4.3.10'
//        classpath 'com.android.tools.build:gradle:7.2.2'
//        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
//    }
//}

allprojects {
    repositories {
        google()
        mavenCentral()

        // // Zoho SalesIQ Mobilisten
        // maven {
        //     url 'https://maven.zohodl.com'
        // }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
