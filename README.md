## FluxStore strapi
- Document & Support Ticket: https://support.inspireui.com
- Changelog: https://products.inspireui.com/fluxstore-strapi/changelog/
- Youtube video guide: http://youtube.com/inspireui
- Website: https://fluxstore.app
- Company website: https://inspireui.com
- Fuxbuilder website: https://fluxbuilder.com

## Download package included:
- Flutter project: (open current folder by Android Studio or VS Code)
- Figma design file: resource/Design/Fluxstore.fig
- Animate splashscreen file: resource/Design/splashscreen.riv (https://rive.app)
- Firebase extension: resource/Firebase Functions

## Download free Fluxbuilder:
- Fluxbuilder is available on following device: Windows 10 and Mac OS
- Download the latest version https://github.com/inspireui/fluxbuilder/releases
