/// Extended set of localized date/time patterns for locale ti similar to locale en.
const tiLocaleDatePatterns = {
  'd': 'd',
  'E': 'ccc',
  'EEEE': 'cccc',
  'LLL': 'LLL',
  'LLLL': 'LLLL',
  'M': 'L',
  'Md': 'M/d',
  'MEd': 'EEE, M/d',
  'MMM': 'LLL',
  'MMMd': 'MMM d',
  'MMMEd': 'EEE, MMM d',
  'MMMM': 'LLLL',
  'MMMMd': 'MMMM d',
  'MMMMEEEEd': 'EEEE, MMMM d',
  'QQQ': 'QQQ',
  'QQQQ': 'QQQQ',
  'y': 'y',
  'yM': 'M/y',
  'yMd': 'M/d/y',
  'yMEd': 'EEE, M/d/y',
  'yMMM': 'MMM y',
  'yMMMd': 'MMM d, y',
  'yMMMEd': 'EEE, MMM d, y',
  'yMMMM': 'MMMM y',
  'yMMMMd': 'MMMM d, y',
  'yMMMMEEEEd': 'EEEE, MMMM d, y',
  'yQQQ': 'QQQ y',
  'yQQQQ': 'QQQQ y',
  'H': 'HH',
  'Hm': 'HH:mm',
  'Hms': 'HH:mm:ss',
  'j': 'h a',
  'jm': 'h:mm a',
  'jms': 'h:mm:ss a',
  'jmv': 'h:mm a v',
  'jmz': 'h:mm a z',
  'jz': 'h a z',
  'm': 'm',
  'ms': 'mm:ss',
  's': 's',
  'v': 'v',
  'z': 'z',
  'zzzz': 'zzzz',
  'ZZZZ': 'ZZZZ',
};

const tiDateSymbols = <String, dynamic>{
  'NAME': 'ti',
  'ERAS': <dynamic>[
    'BC',
    'AD',
  ],
  'ERANAMES': <dynamic>[
    'Before Christ',
    'Anno Domini',
  ],
  'NARROWMONTHS': <dynamic>[
    'J',
    'F',
    'M',
    'A',
    'M',
    'J',
    'J',
    'A',
    'S',
    'O',
    'N',
    'D',
  ],
  'STANDALONENARROWMONTHS': <dynamic>[
    'J',
    'F',
    'M',
    'A',
    'M',
    'J',
    'J',
    'A',
    'S',
    'O',
    'N',
    'D',
  ],
  'MONTHS': <dynamic>[
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ],
  'STANDALONEMONTHS': <dynamic>[
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ],
  'SHORTMONTHS': <dynamic>[
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ],
  'STANDALONESHORTMONTHS': <dynamic>[
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ],
  'WEEKDAYS': <dynamic>[
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
  ],
  'STANDALONEWEEKDAYS': <dynamic>[
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
  ],
  'SHORTWEEKDAYS': <dynamic>[
    'Sun',
    'Mon',
    'Tue',
    'Wed',
    'Thu',
    'Fri',
    'Sat',
  ],
  'STANDALONESHORTWEEKDAYS': <dynamic>[
    'Sun',
    'Mon',
    'Tue',
    'Wed',
    'Thu',
    'Fri',
    'Sat',
  ],
  'NARROWWEEKDAYS': <dynamic>[
    'S',
    'M',
    'T',
    'W',
    'T',
    'F',
    'S',
  ],
  'STANDALONENARROWWEEKDAYS': <dynamic>[
    'S',
    'M',
    'T',
    'W',
    'T',
    'F',
    'S',
  ],
  'SHORTQUARTERS': <dynamic>[
    'Q1',
    'Q2',
    'Q3',
    'Q4',
  ],
  'QUARTERS': <dynamic>[
    '1st quarter',
    '2nd quarter',
    '3rd quarter',
    '4th quarter',
  ],
  'AMPMS': <dynamic>[
    'AM',
    'PM',
  ],
  'DATEFORMATS': <dynamic>[
    'EEEE, MMMM d, y',
    'MMMM d, y',
    'MMM d, y',
    'M/d/yy',
  ],
  'TIMEFORMATS': <dynamic>[
    'h:mm:ss a zzzz',
    'h:mm:ss a z',
    'h:mm:ss a',
    'h:mm a',
  ],
  'AVAILABLEFORMATS': null,
  'FIRSTDAYOFWEEK': 6,
  'WEEKENDRANGE': <dynamic>[
    5,
    6,
  ],
  'FIRSTWEEKCUTOFFDAY': 5,
  'DATETIMEFORMATS': <dynamic>[
    "{1} 'at' {0}",
    "{1} 'at' {0}",
    '{1}, {0}',
    '{1}, {0}',
  ],
};
