import 'package:flutter/material.dart';
// ignore: depend_on_referenced_packages
import 'package:flutter_localizations/flutter_localizations.dart';

class MaterialLocalizationTi extends GlobalMaterialLocalizations {
  /// Create an instance of the translation bundle for Tigrinya.
  const MaterialLocalizationTi({
    super.localeName = 'ti',
    required super.fullYearFormat,
    required super.compactDateFormat,
    required super.shortDateFormat,
    required super.mediumDateFormat,
    required super.longDateFormat,
    required super.yearMonthFormat,
    required super.shortMonthDayFormat,
    required super.decimalFormat,
    required super.twoDigitZeroPaddedFormat,
  });

  @override
  String get aboutListTileTitleRaw => r'በትር $applicationName ብምኒል';

  @override
  String get alertDialogLabel => 'ኣለምና';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonTooltip => 'ወድቅ';

  @override
  String get calendarModeButtonLabel => 'በትር ናይ ቀለል ዘለዎ';

  @override
  String get cancelButtonLabel => 'ይትዕግወስ';

  @override
  String get closeButtonLabel => 'ዝግበር';

  @override
  String get closeButtonTooltip => 'ዝግበር';

  @override
  String get collapsedIconTapHint => 'ነዛሪ';

  @override
  String get continueButtonLabel => 'ድምጺ';

  @override
  String get copyButtonLabel => 'ኣቦሓ';

  @override
  String get cutButtonLabel => 'ግንዛባ';

  @override
  String get dateHelpText => 'ሓሙስ/ታሕሳስ/ዓብዲ';

  @override
  String get dateInputLabel => 'ግተን ኣታ';

  @override
  String get dateOutOfRangeLabel => 'ቀርብ የብልናን።';

  @override
  String get datePickerHelpText => 'ኣታ ምምዕዳ';

  @override
  String get dateRangeEndDateSemanticLabelRaw => r'ኣታቀርብ $fullDate';

  @override
  String get dateRangeEndLabel => 'ኣታቀርብ';

  @override
  String get dateRangePickerHelpText => 'ኣታቕርብ ምምዕዳ';

  @override
  String get dateRangeStartDateSemanticLabelRaw => r'ኣታቕርብ $fullDate';

  @override
  String get dateRangeStartLabel => 'ኣታቕርብ';

  @override
  String get dateSeparator => '/';

  @override
  String get deleteButtonTooltip => 'አጽዓይ';

  @override
  String get dialModeButtonLabel => 'ናብ ንደውስቲ ኣማራ';

  @override
  String get dialogLabel => 'ድያም';

  @override
  String get drawerLabel => 'መንጎቻ መኑቦ';

  @override
  String get expandedIconTapHint => 'ነዛሪ';

  @override
  String get firstPageTooltip => 'መጀመሪ ገጽ';

  @override
  String get hideAccountsLabel => 'ናይ መጠንቀቓት ዝደበቕ';

  @override
  String get inputDateModeButtonLabel => 'ኣታ ንምምዕዳ ናይፕይት';

  @override
  String get inputTimeModeButtonLabel => 'ናይ አንፈላን ኣይኮነስ';

  @override
  String get invalidDateFormatLabel => 'ማሕበረ ዘይትምልኩ።';

  @override
  String get invalidDateRangeLabel => 'ዘይትምልኩ ኣታቀርብ።';

  @override
  String get invalidTimeLabel => 'ስምምዕ ናይ ሰዓት ዘይትምልኩ';

  @override
  String get lastPageTooltip => 'መጨረሪ ገጽ';

  @override
  String? get licensesPackageDetailTextFew => null;

  @override
  String? get licensesPackageDetailTextMany => null;

  @override
  String? get licensesPackageDetailTextOne => '1 ኩባንያ';

  @override
  String get licensesPackageDetailTextOther => r'$licenseCount ኩባንያዎች';

  @override
  String? get licensesPackageDetailTextTwo => null;

  @override
  String? get licensesPackageDetailTextZero => 'ኩባንያ የለዎትን';

  @override
  String get licensesPageTitle => 'ኩባንያዎች';

  @override
  String get modalBarrierDismissLabel => 'ናይ መንዎች ዝብል';

  @override
  String get moreButtonTooltip => 'ኣብዚና';

  @override
  String get nextMonthTooltip => 'ቀጽር ዝቐጸ';

  @override
  String get nextPageTooltip => 'መጨረሪ ገጽ';

  @override
  String get okButtonLabel => 'እሞ';

  @override
  String get openAppDrawerTooltip => 'መንጎቻ ናይፕይት ይክእል';

  @override
  String get pageRowsInfoTitleRaw => r'$firstRow–$lastRow ኣብ $rowCount';

  @override
  String get pageRowsInfoTitleApproximateRaw =>
      r'$firstRow–$lastRow ኣብ ትእዛዝ $rowCount';

  @override
  String get pasteButtonLabel => 'ኣቦሓ';

  @override
  String get popupMenuLabel => 'ናይ ምሕዞ ሓደጋ';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get previousMonthTooltip => 'ቀጽር ዝተገቐ';

  @override
  String get previousPageTooltip => 'መጨረሪ ገጽ';

  @override
  String get refreshIndicatorSemanticLabel => 'ኣክል';

  @override
  String? get remainingTextFieldCharacterCountFew => null;

  @override
  String? get remainingTextFieldCharacterCountMany => null;

  @override
  String? get remainingTextFieldCharacterCountOne => '1 ፍልም ዓይነት';

  @override
  String get remainingTextFieldCharacterCountOther =>
      r'$remainingCount ፍልም ዓይነት የብልናን';

  @override
  String? get remainingTextFieldCharacterCountTwo => null;

  @override
  String? get remainingTextFieldCharacterCountZero => 'ፍልም ዓይነት የብልናን';

  @override
  String get reorderItemDown => 'ስዝብ ንማእከል';

  @override
  String get reorderItemLeft => 'ስዝብ ንማደንስ';

  @override
  String get reorderItemRight => 'ስዝብ ንማርግ';

  @override
  String get reorderItemToEnd => 'ስዝብ ኣብ ዝብል';

  @override
  String get reorderItemToStart => 'ስዝብ ኣብ ዘለዎ';

  @override
  String get reorderItemUp => 'ስዝብ ንማድር';

  @override
  String get rowsPerPageTitle => 'ፍሉይ ብሓቱ:';

  @override
  String get saveButtonLabel => 'ኣክል';

  @override
  ScriptCategory get scriptCategory => ScriptCategory.englishLike;

  @override
  String get searchFieldLabel => 'ሰፈረ';

  @override
  String get selectAllButtonLabel => 'ሰፈር ብምዝጋፍ';

  @override
  String get selectYearSemanticsLabel => 'ሓደ ዓመት ሰፈረ';

  @override
  String? get selectedRowCountTitleFew => null;

  @override
  String? get selectedRowCountTitleMany => null;

  @override
  String? get selectedRowCountTitleOne => '1 ንዝምዕየር ናይባሕር';

  @override
  String get selectedRowCountTitleOther => r'$selectedRowCount ንዝምዕየር ኣቦቶች';

  @override
  String? get selectedRowCountTitleTwo => null;

  @override
  String? get selectedRowCountTitleZero => 'ኣቦቶች የለዎትን';

  @override
  String get showAccountsLabel => 'ንመቐብሪ ዝማውዕ';

  @override
  String get showMenuTooltip => 'ምዝጋፍ ኣብዚና';

  @override
  String get signedInLabel => 'ዝተመሓየሸ';

  @override
  String get tabLabelRaw => r'ቃል $tabIndex ኣብ $tabCount';

  @override
  TimeOfDayFormat get timeOfDayFormatRaw => TimeOfDayFormat.h_colon_mm_space_a;

  @override
  String get timePickerDialHelpText => 'ሰዓት ምምዕዳ';

  @override
  String get timePickerHourLabel => 'ሰዓት';

  @override
  String get timePickerHourModeAnnouncement => 'ሰዓት ምምዕዳ ይክእል';

  @override
  String get timePickerInputHelpText => 'ሰዓት ምምዕዳ';

  @override
  String get timePickerMinuteLabel => 'ደቒቕ';

  @override
  String get timePickerMinuteModeAnnouncement => 'ደቒቕ ምምዕዳ ይክእል';

  @override
  String get unspecifiedDate => 'ኣታ';

  @override
  String get unspecifiedDateRange => 'ኣታ ኣብ ቀርብ';

  @override
  String get viewLicensesButtonLabel => 'ኣታታቕ ዝመረቕ';
  @override
  String get keyboardKeyAlt => 'Alt';

  @override
  String get keyboardKeyAltGraph => 'AltGr';

  @override
  String get keyboardKeyBackspace => 'በስኳር';

  @override
  String get keyboardKeyCapsLock => 'Caps Lock';

  @override
  String get keyboardKeyChannelDown => 'መንጎቻ ኣብዚና';

  @override
  String get keyboardKeyChannelUp => 'መንጎቻ ኣብዚና';

  @override
  String get keyboardKeyControl => 'Ctrl';

  @override
  String get keyboardKeyDelete => 'ስድስት';

  String get keyboardKeyEisu => 'ዒሶ';

  @override
  String get keyboardKeyEject => 'Eject';

  @override
  String get keyboardKeyEnd => 'ኣብ መንጎቻ';

  @override
  String get keyboardKeyEscape => 'Esc';

  @override
  String get keyboardKeyFn => 'Fn';

  String get keyboardKeyHangulMode => 'ሃንጉል መዶ';

  String get keyboardKeyHanjaMode => 'ሃንጃ መዶ';

  String get keyboardKeyHankaku => 'ሃንካኩ';

  String get keyboardKeyHiragana => 'ሕራጋና';

  String get keyboardKeyHiraganaKatakana => 'ሕራጋና ካታካና';

  @override
  String get keyboardKeyHome => 'መጀመሪ';

  @override
  String get keyboardKeyInsert => 'ኣተርኣዩ';

  String get keyboardKeyKanaMode => 'ካና መዶ';

  String get keyboardKeyKanjiMode => 'ካንጃ መዶ';

  String get keyboardKeyKatakana => 'ካታካና';

  @override
  String get keyboardKeyMeta => 'Meta';

  @override
  String get keyboardKeyMetaMacOs => 'Command';

  @override
  String get keyboardKeyMetaWindows => 'Win';

  @override
  String get keyboardKeyNumLock => 'Num Lock';

  @override
  String get keyboardKeyNumpad0 => 'ቁ. 0';

  @override
  String get keyboardKeyNumpad1 => 'ቁ. 1';

  @override
  String get keyboardKeyNumpad2 => 'ቁ. 2';

  @override
  String get keyboardKeyNumpad3 => 'ቁ. 3';

  @override
  String get keyboardKeyNumpad4 => 'ቁ. 4';

  @override
  String get keyboardKeyNumpad5 => 'ቁ. 5';

  @override
  String get keyboardKeyNumpad6 => 'ቁ. 6';

  @override
  String get keyboardKeyNumpad7 => 'ቁ. 7';

  @override
  String get keyboardKeyNumpad8 => 'ቁ. 8';

  @override
  String get keyboardKeyNumpad9 => 'ቁ. 9';

  @override
  String get keyboardKeyNumpadAdd => 'ቁ. +';

  @override
  String get keyboardKeyNumpadComma => 'ቁ. ,';

  @override
  String get keyboardKeyNumpadDecimal => 'ቁ. .';

  @override
  String get keyboardKeyNumpadDivide => 'ቁ. /';

  @override
  String get keyboardKeyNumpadEnter => 'ቁ. Enter';

  @override
  String get keyboardKeyNumpadEqual => 'ቁ. =';

  @override
  String get keyboardKeyNumpadMultiply => 'ቁ. *';

  @override
  String get keyboardKeyNumpadParenLeft => 'ቁ. (';

  @override
  String get keyboardKeyNumpadParenRight => 'ቁ. )';

  @override
  String get keyboardKeyNumpadSubtract => 'ቁ. -';

  @override
  String get keyboardKeyPageDown => 'ማዕከል ገጽ';

  @override
  String get keyboardKeyPageUp => 'መጻኢር ገጽ';

  @override
  String get keyboardKeyPower => 'ኣጥንቲኣዊ';

  @override
  String get keyboardKeyPowerOff => 'ኣጥንቲኣዊ ሓደ';

  @override
  String get keyboardKeyPrintScreen => 'ምልክታት';

  String get keyboardKeyRomaji => 'ሮማጂ';

  @override
  String get keyboardKeyScrollLock => 'Scroll Lock';

  @override
  String get keyboardKeySelect => 'ሰፍረ';

  @override
  String get keyboardKeySpace => 'ስፍራ';

  String get keyboardKeyZenkaku => 'ዘንካኩ';

  String get keyboardKeyZenkakuHankaku => 'ዘንካኩ ሃንካኩ';

  @override
  String get menuBarMenuLabel => 'ምና ቦር ምንዳይ';

  @override
  String get bottomSheetLabel => 'ሓበሬታዊ ገለል';

  @override
  String get currentDateLabel => 'ዚምበር';

  @override
  String get keyboardKeyShift => 'መተዓት';

  @override
  String get scrimLabel => 'ስኪርም';

  @override
  String get scrimOnTapHintRaw => r'ዝግበረ $modalRouteContentName';

  @override
  String get collapsedHint => 'ተጠያቂ';

  @override
  String get expandedHint => 'ክፍልፍል';

  @override
  String get expansionTileCollapsedHint => 'ብምዝብል ኣተሓሳስቡ';

  @override
  String get expansionTileCollapsedTapHint => 'ሓዳሽ ሰብዓዕ ኣተሓሳስቡ';

  @override
  String get expansionTileExpandedHint => 'ብምዝብል ኣንፈልጥ';

  @override
  String get expansionTileExpandedTapHint => 'ኣንፈልጥ';

  @override
  String get scanTextButtonLabel => 'ጽቡቕ ኣድሕየ';

  @override
  String get lookUpButtonLabel => 'ኣገልግሎ';

  @override
  String get menuDismissLabel => 'መንቀሳቀስ ኣይተገኝን';

  @override
  String get searchWebButtonLabel => 'ድረገጽ ፈልግ';

  @override
  String get shareButtonLabel => 'ኣገልግሎ...';

  @override
  // TODO: implement clearButtonTooltip
  String get clearButtonTooltip => throw UnimplementedError();

  @override
  // TODO: implement selectedDateLabel
  String get selectedDateLabel => throw UnimplementedError();
}
