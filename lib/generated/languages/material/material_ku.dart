import 'package:flutter/material.dart';
// ignore: depend_on_referenced_packages
import 'package:flutter_localizations/flutter_localizations.dart';

class MaterialLocalizationKu extends GlobalMaterialLocalizations {
  /// Create an instance of the translation bundle for Kurdish.
  const MaterialLocalizationKu({
    super.localeName = 'ku',
    required super.fullYearFormat,
    required super.compactDateFormat,
    required super.shortDateFormat,
    required super.mediumDateFormat,
    required super.longDateFormat,
    required super.yearMonthFormat,
    required super.shortMonthDayFormat,
    required super.decimalFormat,
    required super.twoDigitZeroPaddedFormat,
  });

  @override
  String get aboutListTileTitleRaw => 'دەربارەی \$applicationName';

  @override
  String get alertDialogLabel => 'ئاگادارکردنەوە';

  @override
  String get anteMeridiemAbbreviation => 'پ.ن';

  @override
  String get backButtonTooltip => 'دواوە';

  @override
  String get calendarModeButtonLabel => 'گۆڕین بۆ ڕۆژژمێر';

  @override
  String get cancelButtonLabel => 'هەڵوەشاندنەوه';

  @override
  String get closeButtonLabel => 'داخستن';

  @override
  String get closeButtonTooltip => 'داخستن';

  @override
  String get collapsedIconTapHint => 'فراوانکردن';

  @override
  String get continueButtonLabel => 'بەردەوام بە';

  @override
  String get copyButtonLabel => 'کۆپی';

  @override
  String get cutButtonLabel => 'بڕین';

  @override
  String get dateHelpText => 'mm/dd/yyyy';

  @override
  String get dateInputLabel => 'بەروار بنووسە';

  @override
  String get dateOutOfRangeLabel => 'دەرەوەی مەودایە';

  @override
  String get datePickerHelpText => 'بەروار دیاری بکە';

  @override
  String get dateRangeEndDateSemanticLabelRaw => 'بەرواری کۆتایی \$fullDate';

  @override
  String get dateRangeEndLabel => 'بەرواری کۆتایی';

  @override
  String get dateRangePickerHelpText => 'دەست نیشانکردنی مەودا';

  @override
  String get dateRangeStartDateSemanticLabelRaw =>
      'بەرواری دەستپێکردن \$fullDate';

  @override
  String get dateRangeStartLabel => 'بەرواری دەستپێکردن';

  @override
  String get dateSeparator => '/';

  @override
  String get deleteButtonTooltip => 'سڕینەوە';

  @override
  String get dialModeButtonLabel => 'گۆڕین بۆ دۆخی هەڵبژێری داواکردن';

  @override
  String get dialogLabel => 'دیالۆگ';

  @override
  String get drawerLabel => 'لیستی ڕێنیشاندەر';

  @override
  String get expandedIconTapHint => 'نوشتانەوە';

  @override
  String get firstPageTooltip => 'لاپه‌ڕه‌ی سه‌ره‌تا';

  @override
  String get hideAccountsLabel => 'شاردنەوەی ئەژمێرەکان';

  @override
  String get inputDateModeButtonLabel => 'گۆڕین بۆ نووسین';

  @override
  String get inputTimeModeButtonLabel => 'گۆڕین بۆ دۆخی تێکردنی دەق';

  @override
  String get invalidDateFormatLabel => 'فۆرماتی نادروست.';

  @override
  String get invalidDateRangeLabel => 'مەودایەکی نادروست.';

  @override
  String get invalidTimeLabel => 'کاتێکی دروست بنووسە';

  @override
  String get lastPageTooltip => 'دوایین لاپه‌ڕه‌';

  @override
  String get licensesPackageDetailTextOne => '١ مۆڵەت';

  @override
  String get licensesPackageDetailTextOther => '\$licenseCount مۆڵەت';

  @override
  String get licensesPackageDetailTextZero => 'مۆڵەت نیە';

  @override
  String get licensesPageTitle => 'مۆڵەتەکان';

  @override
  String get modalBarrierDismissLabel => 'دەرکردن';

  @override
  String get moreButtonTooltip => 'زیاتر';

  @override
  String get nextMonthTooltip => 'مانگی داهاتوو';

  @override
  String get nextPageTooltip => 'لاپەڕەی داهاتوو';

  @override
  String get okButtonLabel => 'باشه';

  @override
  String get openAppDrawerTooltip => 'کردنەوەی لیستی ڕێنیشاندەر';

  @override
  String get pageRowsInfoTitleRaw => '\$firstRow–\$lastRow لە \$rowCount';

  @override
  String get pageRowsInfoTitleApproximateRaw =>
      '\$firstRow–\$lastRow تا \$rowCount';

  @override
  String get pasteButtonLabel => 'پەیست';

  @override
  String get popupMenuLabel => 'لیستی دەرکەوتە';

  @override
  String get postMeridiemAbbreviation => 'د.ن';

  @override
  String get previousMonthTooltip => 'مانگی پێشوو';

  @override
  String get previousPageTooltip => 'لاپەڕەی پێشوو';

  @override
  String get refreshIndicatorSemanticLabel => 'نوێکردنەوە';

  @override
  String? get remainingTextFieldCharacterCountFew => null;

  @override
  List<String> get narrowWeekdays => ['ی', 'د', 'س', 'چ', 'پ', 'ه', 'ش'];

  @override
  String? get remainingTextFieldCharacterCountMany => null;

  @override
  String get remainingTextFieldCharacterCountOne => '١ پیت ماوە';

  @override
  String get remainingTextFieldCharacterCountOther =>
      '\$remainingCount پیتەکان ماون';

  @override
  String? get remainingTextFieldCharacterCountTwo => null;

  @override
  String get remainingTextFieldCharacterCountZero => 'هیچ پیتێک نەماوەتەوە';

  @override
  String get reorderItemDown => 'بڕۆ خوارەوە';

  @override
  String get reorderItemLeft => 'بڕۆ لای چەپ';

  @override
  String get reorderItemRight => 'بڕۆ لای راست';

  @override
  String get reorderItemToEnd => 'بڕۆ کۆتایی';

  @override
  String get reorderItemToStart => 'بڕۆ سەرەتا';

  @override
  String get reorderItemUp => 'بڕۆ سەرەوە';

  @override
  String get rowsPerPageTitle => 'ڕیزەکان بۆ هەر پەڕەیەک:';

  @override
  String get saveButtonLabel => 'هەڵگرتن';

  @override
  ScriptCategory get scriptCategory => ScriptCategory.tall;

  @override
  String get searchFieldLabel => 'گەڕان';

  @override
  String get selectAllButtonLabel => 'هەموو هەڵبژێرە';

  @override
  String get selectYearSemanticsLabel => 'ساڵ هەڵبژێرە';

  @override
  String? get selectedRowCountTitleFew => null;

  @override
  String? get selectedRowCountTitleMany => null;

  @override
  String get selectedRowCountTitleOne => '١ دانە هەڵبژێردرا';

  @override
  String get selectedRowCountTitleOther => '\$selectedRowCount هەڵبژێردراو';

  @override
  String? get selectedRowCountTitleTwo => null;

  @override
  String get selectedRowCountTitleZero => 'هیچ هەڵنەبژێراوە';

  @override
  String get showAccountsLabel => 'پیشاندانی ئەژمێرەکان';

  @override
  String get showMenuTooltip => 'پیشاندانی پێڕست';

  @override
  String get signedInLabel => 'چوونە ژوورەوە';

  @override
  String get tabLabelRaw => 'خشتەبەندی \$tabIndex لە \$tabCount';

  @override
  TimeOfDayFormat get timeOfDayFormatRaw => TimeOfDayFormat.h_colon_mm_space_a;

  @override
  String get timePickerDialHelpText => 'کات هەڵبژێرە';

  @override
  String get timePickerHourLabel => 'کاتژمێر';

  @override
  String get timePickerHourModeAnnouncement => 'کاتژمێر هەڵبژێرە';

  @override
  String get timePickerInputHelpText => 'کات بنووسە';

  @override
  String get timePickerMinuteLabel => 'خولەک';

  @override
  String get timePickerMinuteModeAnnouncement => 'خولەک هەڵبژێرە';

  @override
  String get unspecifiedDate => 'بەروار';

  @override
  String get unspecifiedDateRange => 'مەودای بەروار';

  @override
  String get viewLicensesButtonLabel => 'پیشاندانی مۆڵەتەکان';

  @override
  String get keyboardKeyAlt => 'Alt';

  @override
  String get keyboardKeyAltGraph => 'AltGr';

  @override
  String get keyboardKeyBackspace => 'دووبارەکانی بۆ';

  @override
  String get keyboardKeyCapsLock => 'قەبەلەت پەڵەو';

  @override
  String get keyboardKeyChannelDown => 'کەناڵ لانی خوارەوە';

  @override
  String get keyboardKeyChannelUp => 'کەناڵ لانی سەرەوە';

  @override
  String get keyboardKeyControl => 'Ctrl';

  @override
  String get keyboardKeyDelete => 'سڕینەوە';

  String get keyboardKeyEisu => 'Eisū';

  @override
  String get keyboardKeyEject => 'وەرگرتن';

  @override
  String get keyboardKeyEnd => 'کوتایی';

  @override
  String get keyboardKeyEscape => 'فرار';

  @override
  String get keyboardKeyFn => 'Fn';

  String get keyboardKeyHangulMode => 'ڕێچخونی هانگول';

  String get keyboardKeyHanjaMode => 'ڕێچخونی هانجا';

  String get keyboardKeyHankaku => 'هانکاکو';

  String get keyboardKeyHiragana => 'هیراگانا';

  String get keyboardKeyHiraganaKatakana => 'هیراگانا کاتاکانا';

  @override
  String get keyboardKeyHome => 'ماڵ';

  @override
  String get keyboardKeyInsert => 'ناردن';

  String get keyboardKeyKanaMode => 'ڕێچخونی کانا';

  String get keyboardKeyKanjiMode => 'ڕێچخونی کانجی';

  String get keyboardKeyKatakana => 'کاتاکانا';

  @override
  String get keyboardKeyMeta => 'Meta';

  @override
  String get keyboardKeyMetaMacOs => 'Command';

  @override
  String get keyboardKeyMetaWindows => 'Win';

  @override
  String get keyboardKeyNumLock => 'قفلی ژمارەکان';

  @override
  String get keyboardKeyNumpad0 => 'ژمارەی 0';

  @override
  String get keyboardKeyNumpad1 => 'ژمارەی 1';

  @override
  String get keyboardKeyNumpad2 => 'ژمارەی 2';

  @override
  String get keyboardKeyNumpad3 => 'ژمارەی 3';

  @override
  String get keyboardKeyNumpad4 => 'ژمارەی 4';

  @override
  String get keyboardKeyNumpad5 => 'ژمارەی 5';

  @override
  String get keyboardKeyNumpad6 => 'ژمارەی 6';

  @override
  String get keyboardKeyNumpad7 => 'ژمارەی 7';

  @override
  String get keyboardKeyNumpad8 => 'ژمارەی 8';

  @override
  String get keyboardKeyNumpad9 => 'ژمارەی 9';

  @override
  String get keyboardKeyNumpadAdd => 'ژمارەی +';

  @override
  String get keyboardKeyNumpadComma => 'ژمارەی ,';

  @override
  String get keyboardKeyNumpadDecimal => 'ژمارەی .';

  @override
  String get keyboardKeyNumpadDivide => 'ژمارەی /';

  @override
  String get keyboardKeyNumpadEnter => 'ژمارەی Enter';

  @override
  String get keyboardKeyNumpadEqual => 'ژمارەی =';

  @override
  String get keyboardKeyNumpadMultiply => 'ژمارەی *';

  @override
  String get keyboardKeyNumpadParenLeft => 'ژمارەی (';

  @override
  String get keyboardKeyNumpadParenRight => 'ژمارەی )';

  @override
  String get keyboardKeyNumpadSubtract => 'ژمارەی -';

  @override
  String get keyboardKeyPageDown => 'پەڕەی خوارەوە';

  @override
  String get keyboardKeyPageUp => 'پەڕەی سەرەوە';

  @override
  String get keyboardKeyPower => 'بانگ';

  @override
  String get keyboardKeyPowerOff => 'کچک';

  @override
  String get keyboardKeyPrintScreen => 'پرینت';

  String get keyboardKeyRomaji => 'ڕوماجی';

  @override
  String get keyboardKeyScrollLock => 'قفڵی پوختەکان';

  @override
  String get keyboardKeySelect => 'هەڵبژاردن';

  @override
  String get keyboardKeySpace => 'فاص';

  String get keyboardKeyZenkaku => 'ژەنکاکو';

  String get keyboardKeyZenkakuHankaku => 'ژەنکاکو هانکاکو';

  @override
  String get menuBarMenuLabel => 'مێنیوی پانێی مێنیو';

  @override
  String get bottomSheetLabel => 'شیتی خواراو ';

  @override
  String get currentDateLabel => 'ئه‌مڕوو';

  @override
  String get keyboardKeyShift => 'چاو';

  @override
  String get scrimLabel => 'سکریم';

  @override
  String get scrimOnTapHintRaw => 'داخستن';

  @override
  String get collapsedHint => 'پشتوانە';

  @override
  String get expandedHint => 'گواستنەوە';

  @override
  String get expansionTileCollapsedHint => 'بۆ پشتوانە کردن دووبارە کرتە بکە';

  @override
  String get expansionTileCollapsedTapHint =>
      'زیاتر بۆ وردەکاریەکان پشتوانە بکە';

  @override
  String get expansionTileExpandedHint => 'بۆ گواستنەوە دووبارە کرتە بکە';

  @override
  String get expansionTileExpandedTapHint => 'گواستنەوە';

  @override
  String get scanTextButtonLabel => 'دەقی سکان کردن';

  @override
  String get lookUpButtonLabel => 'Lêgerîn';

  @override
  String get menuDismissLabel => 'Menuyê rakirin';

  @override
  String get searchWebButtonLabel => 'Lêgerîna Weşanê';

  @override
  String get shareButtonLabel => 'Pêşîniya kirin...';

  @override
  // TODO: implement clearButtonTooltip
  String get clearButtonTooltip => throw UnimplementedError();

  @override
  // TODO: implement selectedDateLabel
  String get selectedDateLabel => throw UnimplementedError();
}
