import 'package:flutter/cupertino.dart';
import 'package:inspireui/utils/event_bus.dart';
import 'package:provider/provider.dart';

import '../../common/events.dart';
import '../../data/boxes.dart';
import '../../generated/l10n.dart';
import '../../models/user_model.dart';
import '../../services/services.dart';

mixin DeleteAccountMixin {
  Future<bool> showConfirmDeleteAccountDialog(BuildContext context) async {
    return await showCupertinoDialog(
          context: context,
          barrierDismissible: false,
          useRootNavigator: false,
          builder: (ctx) => CupertinoAlertDialog(
            title: Text(S.current.deleteAccount),
            content: Text(S.current.areYouSureDeleteAccount),
            actions: [
              CupertinoDialogAction(
                isDefaultAction: true,
                isDestructiveAction: true,
                onPressed: () {
                  var loggedInUser = UserBox().userInfo;
                  final user =
                      Provider.of<UserModel>(context, listen: false).user;

                  Services().api.updateUserInfo({
                    'user_id': user?.id,
                    'blocked': true,
                  }, loggedInUser?.cookie);

                  Services().firebase.deleteAccount();

                  eventBus.fire(const EventExpiredCookie());

                  Navigator.of(ctx).pop(true);
                },
                child: Text(S.current.ok),
              ),
              CupertinoDialogAction(
                isDefaultAction: false,
                isDestructiveAction: false,
                onPressed: () => Navigator.of(ctx).pop(false),
                child: Text(S.current.cancel),
              )
            ],
          ),
        ) ??
        false;
  }
}
