import 'package:flutter/cupertino.dart';

import '../../generated/l10n.dart';

mixin CancelOrderMixin {
  Future<bool> showCancelOrderDialog(BuildContext context) async {
    return await showCupertinoDialog(
          context: context,
          barrierDismissible: false,
          useRootNavigator: false,
          builder: (ctx) => CupertinoAlertDialog(
            title: Text(S.current.cancelOrder),
            content: Text(S.current.areYouSureCancelOrder),
            actions: [
              CupertinoDialogAction(
                isDefaultAction: true,
                isDestructiveAction: true,
                onPressed: () {
                  Navigator.of(ctx).pop(true);
                },
                child: Text(S.current.ok),
              ),
              CupertinoDialogAction(
                isDefaultAction: false,
                isDestructiveAction: false,
                onPressed: () => Navigator.of(ctx).pop(false),
                child: Text(S.current.cancel),
              )
            ],
          ),
        ) ??
        false;
  }
}
