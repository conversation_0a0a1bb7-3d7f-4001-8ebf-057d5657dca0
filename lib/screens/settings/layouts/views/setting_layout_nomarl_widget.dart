import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:fstore/screens/settings/widgets/setting_item/setting_item_widget.dart';
import 'package:quickalert/quickalert.dart';

import '../../../../common/config.dart';
import '../../../../common/constants.dart';
import '../../../../common/tools.dart';
import '../../../../generated/l10n.dart';
import '../../../common/delete_account_mixin.dart';
import '../mixins/branch_mixin.dart';
import '../mixins/setting_nomarl_mixin.dart';
import '../setting_builder_layout.dart';

class SettingLayoutNormalWidget extends StatefulWidget {
  const SettingLayoutNormalWidget({
    super.key,
    required this.dataSettings,
  });

  final DataSettingScreen dataSettings;

  @override
  State<SettingLayoutNormalWidget> createState() =>
      _SettingLayoutNormalWidgetState();
}

class _SettingLayoutNormalWidgetState extends State<SettingLayoutNormalWidget>
    with DeleteAccountMixin, SettingNormalMixin, BranchMixin {
  @override
  DataSettingScreen get dataSettings => widget.dataSettings;

  @override
  BuildContext get buildContext => context;

  @override
  ScrollController get scrollController => PrimaryScrollController.of(context);

  @override
  Widget build(BuildContext context) {
    const radiusSize = 25.0;

    return CustomScrollView(
      controller: scrollController,
      slivers: <Widget>[
        appBarWidget,
        // Items
        SliverList(
          delegate: SliverChildListDelegate(
            <Widget>[
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    // UserInfo
                    if (userInfoWidget != null)
                      userInfoWidget!
                    else
                      const SizedBox(height: 30.0),

                    Text(
                      S.of(context).generalSetting,
                      style: const TextStyle(
                          fontSize: 18, fontWeight: FontWeight.w600),
                    ),
                    const SizedBox(height: 10.0),
                  ],
                ),
              ),
              Container(
                margin: marginHorizontalItemDynamic,
                decoration: decoration,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    /// Vendor
                    vendorAdminWidget,

                    /// Branch
                    branchWidget,

                    /// Render some extra menu for Vendor.
                    /// Currently support WCFM & Dokan. Will support WooCommerce soon.
                    ...someExtraMenuForVendorWidget,

                    deliveryBoyWidget,

                    /// Render custom Wallet feature
                    // webViewProfileWidget,

                    /// render some extra menu for Listing
                    ...settingListingWidget,

                    /// render list of dynamic menu
                    /// this could be manage from the Fluxbuilder
                    ...listDynamicItems,

                    // TODO-Settings
                    if (currentVendor?.aboutVendor?.privacy != null &&
                        (currentVendor?.aboutVendor?.privacy.isNotEmpty ??
                            false))
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: SettingItemWidget(
                          cardStyle: SettingItemStyle.listTile,
                          icon: Icons.privacy_tip_outlined,
                          title: S.of(context).privacyPolicy,
                          trailing: const Icon(
                            Icons.arrow_forward_ios,
                            size: 18,
                            color: kGrey600,
                          ),
                          onTap: () {
                            QuickAlert.show(
                              title: S.of(context).privacyPolicy,
                              text: currentVendor?.aboutVendor?.privacy,
                              context: context,
                              type: QuickAlertType.info,
                              confirmBtnText: S.of(context).close,
                            );
                          },
                        ),
                      ),

                    // about
                    if (currentVendor?.aboutVendor?.about != null &&
                        (currentVendor?.aboutVendor?.about.isNotEmpty ?? false))
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: SettingItemWidget(
                          cardStyle: SettingItemStyle.listTile,
                          icon: Icons.info_outline,
                          title: S.of(context).aboutUs,
                          trailing: const Icon(
                            Icons.arrow_forward_ios,
                            size: 18,
                            color: kGrey600,
                          ),
                          onTap: () {
                            QuickAlert.show(
                              title: S.of(context).aboutUs,
                              text: currentVendor?.aboutVendor?.about,
                              context: context,
                              type: QuickAlertType.info,
                              confirmBtnText: S.of(context).close,
                            );
                          },
                        ),
                      ),

                    /// Delete account
                    if (deleteAccountItem != null)
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: itemPadding),
                        child: deleteAccountItem,
                      ),

                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 42),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          if (currentVendor?.aboutVendor?.whatsapp != null &&
                              (currentVendor
                                      ?.aboutVendor?.whatsapp.isNotEmpty ??
                                  false))
                            CircleAvatar(
                              radius: radiusSize,
                              backgroundColor: const Color(0xFF25D366),
                              child: IconButton(
                                onPressed: () {
                                  Tools.launchURL(
                                    'https://wa.me/${currentVendor?.aboutVendor?.whatsapp}',
                                  );
                                },
                                icon: const FaIcon(
                                  FontAwesomeIcons.whatsapp,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          if (currentVendor?.aboutVendor?.facebook != null &&
                              (currentVendor
                                      ?.aboutVendor?.facebook.isNotEmpty ??
                                  false))
                            CircleAvatar(
                              radius: radiusSize,
                              backgroundColor: Colors.blue,
                              child: IconButton(
                                onPressed: () {
                                  Tools.launchURL(
                                    currentVendor?.aboutVendor?.facebook,
                                  );
                                },
                                icon: const FaIcon(
                                  FontAwesomeIcons.facebook,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          if (currentVendor?.aboutVendor?.instagram != null &&
                              (currentVendor
                                      ?.aboutVendor?.instagram.isNotEmpty ??
                                  false))
                            CircleAvatar(
                              radius: radiusSize,
                              backgroundColor: Colors.pink,
                              child: IconButton(
                                onPressed: () {
                                  Tools.launchURL(
                                    currentVendor?.aboutVendor?.instagram,
                                  );
                                },
                                icon: const FaIcon(
                                  FontAwesomeIcons.instagram,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          if (currentVendor?.aboutVendor?.tiktok != null &&
                              (currentVendor?.aboutVendor?.tiktok.isNotEmpty ??
                                  false))
                            CircleAvatar(
                              radius: radiusSize,
                              backgroundColor: Colors.black,
                              child: IconButton(
                                onPressed: () {
                                  Tools.launchURL(
                                    currentVendor?.aboutVendor?.tiktok,
                                  );
                                },
                                icon: const FaIcon(
                                  FontAwesomeIcons.tiktok,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),


                  ],
                ),
              ),
              if (logoutItemWidget != null)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20)
                      .copyWith(top: 20),
                  child: logoutItemWidget,
                ),
              const SizedBox(height: 180),
            ],
          ),
        ),
      ],
    );
  }
}
