import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';

import '../../../common/config.dart';
import '../../../data/boxes.dart';
import '../../../models/entities/index.dart';
import '../../../models/user_model.dart';
import '../../../services/index.dart';

class OrderHistoryDetailModel extends ChangeNotifier {
  Order _order;
  List<OrderNote>? _listOrderNote;
  bool _orderNoteLoading = false;
  final User user;
  final _services = Services();

  Order get order => _order;

  List<OrderNote>? get listOrderNote => _listOrderNote;

  bool get orderNoteLoading => _orderNoteLoading;

  OrderHistoryDetailModel({
    required Order order,
    required this.user,
  }) : _order = order {
    fetchImageOfOrder();
  }

  Future<void> fetchImageOfOrder() async {
    await fetchProductItems();
    await fetchImage();
  }

  Future<void> fetchProductItems() async {
    final listProductItem =
        await _services.api.getListProductItemByOrderId(order.id ?? '');
    if (listProductItem.isNotEmpty) {
      _order.lineItems = listProductItem
          .map((e) => (e, ProductQuantityModel(quantity: e.quantity)))
          .toList();
      notifyListeners();
    }
  }

  Future<void> fetchImage() async {
    print('dxgfhy ${_order.createdAt}');
    final firstProduct = _order.lineItems.first;
    if (firstProduct.$1.featuredImage?.isEmpty ?? true) {
      final listImage =
          await _services.api.getImagesByProductId(firstProduct.$1.productId!);
      if (listImage.isNotEmpty) {
        firstProduct.$1.featuredImage = listImage.first;
      }
      notifyListeners();
    }
  }

  Future<void> cancelOrder(BuildContext context) async {
    if (order.status?.isCanceled ?? false) return;
    final userModel = context.read<UserModel>();

    final newOrder = await _services.api
        .cancelOrder(order: order, userCookie: user.cookie, user: userModel);

    //update local orders for guest
    if (newOrder != null && user.cookie == null) {
      var items = UserBox().orders;
      var list = <Map>[];
      if (items.isNotEmpty) {
        for (var item in items) {
          if (item['id'] == newOrder.id) {
            item['status'] = newOrder.status?.content;
          }
          list.add(item);
        }
      }
      UserBox().orders = list;
    }
    if (newOrder != null) {
      newOrder.lineItems = order
          .lineItems; // fix this issue https://github.com/fluxstore/fluxstore-core/issues/667
      _order = newOrder;
      await fetchImageOfOrder();
      final isDefaultLangAr = currentVendor?.config?.defaultLanguage == 'ar';

      await Services().firebase.sendNotification(
          title: isDefaultLangAr ? 'تم إلغاء الطلب' : 'Order Cancelled',
          body: isDefaultLangAr
              ? 'تم إلغاء طلب #${newOrder.number.toString()}'
              : 'Order #${newOrder.number.toString()} has been cancelled',
          userToken: vendorBusinessName.toString(),
          isTopic: true);

      // await Services().firebase.sendNotification(
      //     title: S.of(context).orderCancelled,
      //     body:
      //         '${S.of(context).yourOrderNumber(newOrder.number.toString())} ${S.of(context).hasBeenCanceled}',
      //     userToken: vendorBusinessName.toString(),
      //     isTopic: true);

      notifyListeners();
    }
  }

  Future<void> createRefund() async {
    if (order.status == OrderStatus.refunded) return;
    await _services.api
        .updateOrder(order.id, status: 'refund-req', token: user.cookie)!
        .then((onValue) {
      _order = onValue;
      Services().firebase.firebaseAnalytics?.logRefund(
            orderId: _order.id,
            price: _order.total,
            currency: _order.currencyCode,
            data: _order.lineItems,
          );
      notifyListeners();
    });
  }

  void getOrderNote() async {
    _orderNoteLoading = true;
    notifyListeners();
    _listOrderNote =
        await _services.api.getOrderNote(userId: user.id, orderId: order.id);
    _orderNoteLoading = false;
    notifyListeners();
  }

// void getTracking() {
//   _services.api.getTracking()?.then((onValue) {
//     for (var track in onValue.trackings) {
//       if (track.orderId == order.number) {
//         tracking = track.trackingNumber;
//         notifyListeners();
//         return;
//       }
//     }
//   });
// }
}
