import 'package:flutter/material.dart';
import 'package:inspireui/widgets/coupon_card.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../common/config.dart';
import '../../../common/constants.dart';
import '../../../common/tools.dart';
import '../../../generated/l10n.dart';
import '../../../models/entities/coupon.dart';
import '../../../models/index.dart' show AppModel, UserModel;
import '../../../services/index.dart';
import '../../base_screen.dart';
import 'empty_coupon.dart';

class CouponList extends StatefulWidget {
  final String? couponCode;
  final Function(String couponCode)? onSelect;
  final bool isFromCart;
  final bool isModal;

  const CouponList({
    super.key,
    this.couponCode,
    this.onSelect,
    this.isFromCart = false,
    this.isModal = false,
  });

  @override
  BaseScreen<CouponList> createState() => _CouponListState();
}

class _CouponListState extends BaseScreen<CouponList> {
  final services = Services();
  final _couponTextController = TextEditingController();

  List<PromoCodeModel> promoCodes = [];

  final Map<String?, Coupon> _couponsMap = {};

  List<Coupon> coupons = [];
  String? email;
  bool isFetching = false;
  int currentPage = 1;

  bool needToReload = false;

  final refreshController = RefreshController(initialRefresh: false);

  @override
  void afterFirstLayout(BuildContext context) {
    if (widget.couponCode != null) {
      needToReload = true;
      _couponTextController.text = widget.couponCode!;
      setState(() {});
    }

    email = Provider.of<UserModel>(context, listen: false).user?.email;
    // _displayCoupons(context);

    /// Fetch new coupons.
    // fetchCoupons(search: widget.couponCode);
    promoCodes =
        promoCodesList.where((element) => element.showInList == true).toList();

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkTheme = theme.brightness == Brightness.dark;
    final model = Provider.of<AppModel>(context);

    final listCouponWidget = isFetching
        ? kLoadingWidget(context)
        : promoCodes.isEmpty
            ? const Center(child: EmptyCoupon())
            : ListView.builder(
                itemCount: promoCodes.length,
                itemBuilder: (BuildContext context, int index) {
                  final coupon = promoCodes[index];

                  return Container(
                    margin: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Card(
                      elevation: 4.0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10.0),
                      ),
                      child: ListTile(
                        contentPadding: const EdgeInsets.all(16.0),
                        leading: CircleAvatar(
                          backgroundColor: Theme.of(context).primaryColorLight,
                          child: Text(
                            coupon.code[0].toUpperCase(),
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                        ),
                        title: Text(
                          coupon.code,
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        subtitle: Text(
                          coupon.isPercent
                              ? '${coupon.discount}% ${S.of(context).off}'
                              : PriceTools.getCurrencyFormatted(
                                    coupon.discount,
                                    model.currencyRate,
                                    currency: model.currency,
                                  ) ??
                                  '',
                          // _getCouponAmount(context, coupon, PriceTools.getCurrencyFormatted),
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        trailing: ElevatedButton(
                          onPressed: () {
                            widget.onSelect?.call(coupon.code);

                            selectedPromoCode = coupon;

                            Navigator.of(context).pop();
                          },
                          child: Text(S.of(context).useNow),
                        ),
                      ),
                    ),
                  );
                },
              );

    final textField = TextField(
      onChanged: (_) {
        if (needToReload) {
          needToReload = false;
          currentPage = 1;
          refreshController.requestLoading();
        }
        final searchQuery = _couponTextController.text.toLowerCase();

        if (searchQuery.isEmpty) {
          promoCodes = promoCodesList
              .where((element) => element.showInList == true)
              .toList();
        }

        promoCodes = promoCodesList
            .where(
                (element) => element.code.toLowerCase().contains(searchQuery))
            .where((element) => element.showInList == true)
            .toList();

        setState(() {});
      },
      controller: _couponTextController,
      decoration: InputDecoration(
        fillColor: Theme.of(context).colorScheme.secondary,
        border: InputBorder.none,
        hintText: S.of(context).couponCode,
        focusColor: Theme.of(context).colorScheme.secondary,
        suffixIcon: _couponTextController.text.isNotEmpty
            ? IconButton(
                onPressed: () {
                  _couponTextController.clear();
                  if (needToReload) {
                    needToReload = false;
                    currentPage = 1;
                    try {
                      refreshController.requestLoading();
                    } catch (_) {}
                  }

                  promoCodes = promoCodesList;

                  setState(() {});
                },
                icon: Icon(
                  Icons.cancel,
                  color:
                      Theme.of(context).colorScheme.secondary.withOpacity(0.7),
                  size: 20,
                ),
              )
            : null,
      ),
    );

    if (widget.isModal) {
      return ListCouponLayoutModal(
        textField: textField,
        theme: theme,
        listCouponWidget: listCouponWidget,
      );
    }

    return ListCouponLayoutScreen(
      isDarkTheme: isDarkTheme,
      theme: theme,
      textField: textField,
      listCouponWidget: listCouponWidget,
    );
  }

  String _getCouponTypeTitle(Coupon coupon, CouponTranslate? trans) {
    if (coupon.isPercentageDiscount) {
      return trans?.discount ?? 'Discount';
    }
    if (coupon.isFixedCartDiscount) {
      return trans?.fixedCartDiscount ?? 'Fixed Cart Discount';
    }
    if (coupon.isFixedProductDiscount) {
      return trans?.fixedProductDiscount ?? 'Fixed Product Discount';
    }
    return coupon.code.toString().toUpperCase();
  }
}

class ListCouponLayoutScreen extends StatelessWidget {
  const ListCouponLayoutScreen({
    super.key,
    required this.isDarkTheme,
    required this.theme,
    required this.textField,
    required this.listCouponWidget,
  });

  final bool isDarkTheme;
  final ThemeData theme;
  final TextField textField;
  final Widget listCouponWidget;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          isDarkTheme ? theme.colorScheme.surface : theme.cardColor,
      appBar: AppBar(
        backgroundColor:
            isDarkTheme ? theme.colorScheme.surface : theme.cardColor,
        leading: IconButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          icon: const Icon(
            Icons.arrow_back_ios,
            size: 22,
          ),
        ),
        titleSpacing: 0.0,
        title: Container(
          height: 40,
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColorLight,
            borderRadius: BorderRadius.circular(20),
          ),
          padding: const EdgeInsets.only(left: 24),
          margin: const EdgeInsets.only(right: 24.0),
          child: textField,
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: Container(
              color: isDarkTheme
                  ? theme.colorScheme.surface
                  : theme.primaryColorLight,
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: listCouponWidget,
            ),
          ),
        ],
      ),
    );
  }
}

class ListCouponLayoutModal extends StatelessWidget {
  const ListCouponLayoutModal({
    super.key,
    required this.textField,
    required this.theme,
    required this.listCouponWidget,
  });

  final TextField textField;
  final ThemeData theme;
  final Widget listCouponWidget;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            S.of(context).selectVoucher,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontSize: 18,
                  height: 28 / 18,
                  fontWeight: FontWeight.w400,
                ),
          ),
          Text(
            S.of(context).descriptionEnterVoucher,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontSize: 14,
                  height: 20 / 14,
                  fontWeight: FontWeight.w400,
                ),
          ),
          Container(
            margin: const EdgeInsets.symmetric(vertical: 20),
            decoration: BoxDecoration(
              border: Border.all(
                width: 1,
                color: kGrey200,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
              child: textField,
            ),
          ),
          Expanded(
            child: Container(
              color: theme.colorScheme.surface,
              child: listCouponWidget,
            ),
          ),
          const SizedBox(height: 16),
          OutlinedButton(
            onPressed: Navigator.of(context).pop,
            child: Text(S.of(context).cancel),
          )
        ],
      ),
    );
  }
}
