import 'package:flutter/material.dart';
import 'package:transparent_image/transparent_image.dart';

import '../../../../common/config.dart';
import '../../../../common/constants.dart';
import '../../../../context_extensions.dart';
import '../../../../models/entities/category.dart';
import '../../../../widgets/common/flux_image.dart';
import '../../../../widgets/common/parallax_image.dart';

class RestaurantCategoryCardItem extends StatelessWidget {
  final Category category;
  final bool enableParallax;
  final double? parallaxImageRatio;
  final VoidCallback? onTap;

  const RestaurantCategoryCardItem(
    this.category, {
    this.enableParallax = false,
    this.parallaxImageRatio,
    this.onTap,
  });

  /// Render category Image support caching on ios/android
  /// also fix loading on Web
  Widget renderCategoryImage(maxWidth) {
    final image = category.image ?? '';
    if (image.isEmpty) return const SizedBox();

    // var imageProxy = '$kImageProxy${maxWidth}x,q30/';

    if (image.contains('http') && kIsWeb) {
      return FadeInImage.memoryNetwork(
        image: image,
        fit: BoxFit.cover,
        width: maxWidth,
        height: maxWidth * 0.35,
        placeholder: kTransparentImage,
      );
    }

    return FluxImage(
      imageUrl: category.image!,
      fit: BoxFit.cover,
      width: maxWidth,
      height: maxWidth * 0.35,
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    return GestureDetector(
      onTap: onTap,
      child: LayoutBuilder(
        builder: (context, constraints) {
          if (enableParallax) {
            return Container(
              height: constraints.maxWidth * 0.35,
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.only(left: 10, right: 10),
              margin: const EdgeInsets.only(bottom: 10),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: ParallaxImage(
                  image: category.image ?? '',
                  name: category.name ?? '',
                  ratio: 2.2,
                  width: MediaQuery.of(context).size.width,
                  fit: BoxFit.fitWidth,
                ),
              ),
            );
          }

          return Container(
            width: screenSize.width,
            height: constraints.maxWidth * 0.35,
            margin: const EdgeInsets.only(bottom: 20),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              children: [
                SizedBox(
                  width: constraints.maxWidth * 0.4,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      SizedBox(
                        height: constraints.maxWidth * 0.35,
                        child: Transform.flip(
                          flipX: context.isRtl ? true : false,
                          child: ClipRRect(
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(20),
                              bottomLeft: Radius.circular(20),
                            ),
                            child: CustomPaint(
                              size: Size(screenSize.width, 180),
                              painter: CurvedPainter(
                                  Theme.of(context).colorScheme.primary),
                            ),
                          ),
                        ),
                      ),
                      // Padding(
                      //   padding: context.isTablet
                      //       ? EdgeInsets.zero
                      //       : const EdgeInsets.all(50.0),
                      //   child: CircleAvatar(
                      //     radius: context.isTablet ? 90 : 55,
                      //     backgroundColor: Colors.transparent,
                      //     child: ClipRRect(
                      //         borderRadius: BorderRadius.circular(100),
                      //         child: FluxImage(
                      //           imageUrl: category.image ?? '',
                      //           height: 200,
                      //           width: 200,
                      //           fit: BoxFit.cover,
                      //         )),
                      //   ),
                      // ),
                      CircleAvatar(
                        radius: context.isTablet
                            ? 90
                            : isIdea2App
                                ? 70
                                : 55,
                        backgroundColor: Colors.transparent,
                        child: Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(100),
                            child: FluxImage(
                              imageUrl: category.image ?? '',
                              height: context.isTablet ? 300 : 200,
                              width: context.isTablet ? 300 : 200,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),
                      // Container(
                      //   width: constraints.maxWidth * 0.35,
                      //   height: constraints.maxWidth * 0.35,
                      //   child: renderCategoryImage(constraints.maxWidth),
                      // ),
                    ],
                  ),
                ),
                const SizedBox(
                  width: 15,
                ),
                Expanded(
                  child: Text(
                    category.name ?? '',
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 40,
                      fontWeight: FontWeight.bold,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
                const SizedBox(
                  width: 15,
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

class CurvedPainter extends CustomPainter {
  final Color primaryColor;

  CurvedPainter(this.primaryColor);

  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint();
    paint.color = primaryColor;
    paint.style = PaintingStyle.fill; // Change this to fill

    var path = Path();

    path.moveTo(size.width * 0.8, 0.4);
    path.quadraticBezierTo(size.width * 0.8, size.height * 0.25,
        size.width * 0.5, size.height * 0.5);
    path.quadraticBezierTo(
        size.width * 0.2, size.height * 0.75, size.width * 0.5, size.height);
    path.lineTo(0, size.height);
    path.lineTo(0, 0);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}
