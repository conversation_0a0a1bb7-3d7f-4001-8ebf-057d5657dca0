import 'package:flutter/material.dart';
import 'package:fstore/screens/templates/on_boarding_template_screen.dart';
import 'package:provider/provider.dart';

import '../../common/config.dart';
import '../../common/config/models/onboarding_config.dart';
import '../../common/constants.dart';
import '../../common/constants/local_keys.dart';
import '../../models/app_model.dart';
import '../../services/get_storage_service.dart';
import '../home/<USER>';
import '../onboarding/onboarding_mixin.dart';
import 'widgets/version1/onboarding_v1.dart';
import 'widgets/version2/onboarding_v2.dart';

class OnBoardingScreen extends StatefulWidget {
  const OnBoardingScreen({
    super.key,
  });

  @override
  State<OnBoardingScreen> createState() => _OnBoardingScreenState();
}

class _OnBoardingScreenState extends State<OnBoardingScreen>
    with ChangeLanguage, OnBoardingMixin {
  // Priority read config.json
  OnBoardingConfig get config =>
      Provider.of<AppModel>(context).appConfig?.onBoardingConfig ??
      kOnBoardingConfig;

  @override
  Widget build(BuildContext context) {
    final isTemplateSaved = GetStorageService.hasData(key: LocalKeys.template);

    if (kIsWeb) {
      return const OnBoardingTemplateScreen();
    }

    if (isIdea2App && !isTemplateSaved) {
      return const OnBoardingTemplateScreen();
    }

    if (config.version == 1) {
      return OnBoardingV1(config: config);
    }
    return OnBoardingV2(config: config);
  }
}
