import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fstore/common/config.dart';
import 'package:fstore/common/constants.dart';
import 'package:fstore/common/constants/local_keys.dart';
import 'package:fstore/common/tools/tools.dart';
import 'package:fstore/routes/flux_navigate.dart';
import 'package:fstore/services/get_storage_service.dart';
import 'package:lottie/lottie.dart';
import 'package:universal_html/html.dart' as html;

import '../../context_extensions.dart';

class HomeFloatingButtons extends StatelessWidget {
  const HomeFloatingButtons({super.key});

  @override
  Widget build(BuildContext context) {
    final haveCenteredFloatingButton = isAccessories || isDefault;
    return Padding(
      padding: EdgeInsets.only(bottom: haveCenteredFloatingButton ? 15 : 0),
      child: Theme(
        data: Theme.of(context).copyWith(useMaterial3: true),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            FloatingActionButton.extended(
              key: const Key('getYourApp'),
              heroTag: 'getYourAppHero',
              backgroundColor: const Color(0xFFe8f5fd),
              foregroundColor: Colors.white,
              onPressed: () {
                final template =
                    GetStorageService.getData(key: LocalKeys.template)
                            ?.toString()
                            .capitalize() ??
                        'Clothes';

                final iconByTemplate = {
                  'Clothes': '👕',
                  'Accessories': '💍',
                  'Restaurant': '🍽️',
                  'Market': '🛒',
                  'Grocery': '🍎',
                  'Furniture': '🪑',
                  'Electronics': '📱',
                  'Flowers': '💐',
                  'Gifts': '🎁',
                  'Shoes': '👟',
                  'Medical': '🩺',
                  'Others': '🚀',
                  'General': '🚀',
                  'Default': '🚀',
                };

                final icon = iconByTemplate[template] ?? '🚀';

                final text =
                    'Hello 👋🏻, I’d like to create my own App 🚀📱\n\n❇️ Template: $template $icon';

                Tools.launchURL(
                  'https://wa.me/+201011984272?text=$text',
                );
              },

              label: Text(context.tr.getYourApp,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black,
                  )),
              icon: Lottie.asset(
                'assets/animated/download.json',
                width: 30,
                height: 30,
              ),
              // const Icon(Icons.download),
            ),
            const SizedBox(height: 10),
            FloatingActionButton.extended(
              key: const Key('changeTemplate'),
              heroTag: 'changeTemplateHero',
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              onPressed: () {
                if (kIsWeb) {
                  html.window.history.pushState(null, '', '');
                }
                FluxNavigate.pushNamed(RouteList.templateOnBoarding);
              },
              icon: const Icon(CupertinoIcons.pencil_outline),
              label: Text(context.tr.changeTemplate),
            ),
            const SizedBox(width: 10),
          ],
        ),
      ),
    );
  }
}
