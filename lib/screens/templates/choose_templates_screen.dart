// import 'package:flutter/material.dart';
// import 'package:flutter/scheduler.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
// import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';
// import 'package:fstore/frameworks/strapi/services/strapi_service.dart';
// import 'package:fstore/models/template_model.dart';
//
// import '../../context_extensions.dart';
//
// class ChooseTemplateScreen extends HookWidget {
//   const ChooseTemplateScreen({
//     super.key,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     final isLoaded = useState(false);
//     final templates = useState<List<TemplateModel>>([]);
//
//     Future getTemplates() async {
//       isLoaded.value = false;
//       final templatesData = (await StrapiService.getSettings()).templates ?? [];
//
//       templates.value = templatesData;
//       isLoaded.value = true;
//     }
//
//     useEffect(() {
//       SchedulerBinding.instance.addPostFrameCallback((_) {
//         getTemplates();
//         // if (selectedBusinessType.value == null) {
//         //   selectedBusinessType.value = templates?.firstOrNull;
//         //   isLoaded.value = true;
//         // } else {
//         //   selectedBusinessType.value = templates?.firstWhereOrNull(
//         //       (element) => element.name == selectedBusinessType.value?.name);
//         //   isLoaded.value = true;
//         // }
//       });
//       return () {};
//     }, []);
//
//     return Column(
//       children: [
//         Text(
//           context.tr.chooseTemplate,
//         ),
//         const SizedBox(height: 10),
//         Expanded(
//             child: StaggeredGrid.count(
//                 crossAxisCount: context.screenWidth > 600 ? 4 : 3,
//                 mainAxisSpacing: 4.0,
//                 crossAxisSpacing: 8.0,
//                 children: templates.value.map((template) {
//                   return TemplateCardWidget(
//                     template: template,
//                   );
//                 }).toList()))
//       ],
//     );
//   }
// }
//
// class TemplateCardWidget extends StatelessWidget {
//   final TemplateModel template;
//
//   const TemplateCardWidget({super.key, required this.template});
//
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(8.0),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.grey.withOpacity(0.5),
//             spreadRadius: 1,
//             blurRadius: 2,
//             offset: const Offset(0, 1), // changes position of shadow
//           ),
//         ],
//       ),
//       child: Column(
//         children: [
//           Image.network(
//             template.url,
//             width: double.infinity,
//             height: 150,
//             fit: BoxFit.cover,
//           ),
//           const SizedBox(height: 10),
//           Text(
//             template.name,
//           ),
//           const SizedBox(height: 10),
//           TextButton(
//             onPressed: () {
//               // openURL(template.url);
//             },
//             child: Text(
//               context.tr.chooseTemplate,
//             ),
//           )
//         ],
//       ),
//     );
//   }
// }
