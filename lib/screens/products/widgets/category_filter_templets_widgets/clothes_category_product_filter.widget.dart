import 'package:flutter/material.dart';

class ClothesCategoryProductFilterWidget extends StatelessWidget {
  final String? categoryId;
  final String categoryName;
  final String? categoryImage;
  final bool isSelected;

  final Function(String?)? onTap;
  const ClothesCategoryProductFilterWidget({
    super.key,
    this.categoryId,
    required this.categoryName,
    this.categoryImage,
    this.isSelected = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    var highlightColor =
        isSelected ? Theme.of(context).colorScheme.primary : Colors.transparent;
    return GestureDetector(
      onTap: () => onTap?.call(categoryId),
      child: Column(
        children: [
          AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
            margin: const EdgeInsets.all(5),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: highlightColor,
                width: 2,
              ),
            ),
            child: categoryImage != null
                ? Text(
                    categoryName.toUpperCase(),
                    maxLines: 2,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                    textAlign: TextAlign.center,
                    softWrap: true,
                    overflow: TextOverflow.visible,
                  )
                : Center(
                    child: Text(
                      categoryName.toUpperCase(),
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            letterSpacing: 0.5,
                            fontWeight: FontWeight.w500,
                            color: Theme.of(context).colorScheme.secondary,
                          ),
                    ),
                  ),
          ),
        ],
      ),
    );
  }
}
