import 'package:flutter/material.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';

import '../../../../widgets/common/flux_image.dart' show FluxImage;

class RestaurantCategoryProductFilterWidget extends StatelessWidget {
  final String? categoryId;
  final String categoryName;
  final String? categoryImage;
  final bool isSelected;

  final Function(String?)? onTap;

  const RestaurantCategoryProductFilterWidget({
    super.key,
    this.categoryId,
    required this.categoryName,
    this.categoryImage,
    this.isSelected = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    var highlightColor =
        isSelected ? Theme.of(context).colorScheme.primary : Colors.transparent;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: GestureDetector(
        onTap: () => onTap?.call(categoryId),
        child: Column(
          children: [
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.all(4),
              margin: const EdgeInsets.all(5),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: highlightColor,
                  width: 2,
                ),
              ),
              child: categoryImage != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(100),
                      child: SizedBox(
                        width: context.screenWidth * 0.15,
                        height: context.screenWidth * 0.15,
                        child: FluxImage(
                          imageUrl: categoryImage!,
                          fit: BoxFit.cover,
                        ),
                      ),
                    )
                  : Center(
                      child: Text(
                        categoryName.toUpperCase(),
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                              letterSpacing: 0.5,
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).colorScheme.secondary,
                            ),
                      ),
                    ),
            ),
            const SizedBox(
              height: 5,
            ),
            SizedBox(
              width: context.screenWidth * 0.25,
              child: Text(
                categoryName.toUpperCase(),
                maxLines: 2,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                textAlign: TextAlign.center,
                softWrap: true,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
