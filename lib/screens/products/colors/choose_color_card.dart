import 'package:flutter/material.dart';

class ChooseColorCard extends StatelessWidget {
  final String color;
  final bool isOutOffStock;

  const ChooseColorCard({
    super.key,
    required this.color,
    this.isOutOffStock = false,
  });

  @override
  Widget build(BuildContext context) {
    if (color.isEmpty) return const SizedBox.shrink();

    return Container(
      width: 30,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isOutOffStock
            ? Color(int.tryParse(color) ?? 0).withOpacity(0.5)
            : Color(int.tryParse(color) ?? 0),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.4),
            spreadRadius: 1,
            blurRadius: 1,
            offset: const Offset(0, 1), // changes position of shadow
          ),
        ],
      ),
    );
  }
}
