import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import '../../../common/constants.dart';
import '../../../common/tools/tools.dart';
import '../../../models/vendor/extra_setting_model.dart';
import '../../checkout/widgets/convet_city_lang.dart';

import 'choose_color_card.dart';

const whiteColor = '0xFFffffff';
const blackColor = '0xFF000000';

class ProductColorsList extends StatefulWidget {
  final List<ExtraSettingsModel> colors;
  final ValueNotifier<String>? selectedColor;
  final Function setState;

  const ProductColorsList(
      {super.key,
      required this.selectedColor,
      required this.colors,
      required this.setState});

  @override
  State<ProductColorsList> createState() => _ProductColorsListState();
}

class _ProductColorsListState extends State<ProductColorsList> {
  @override
  Widget build(BuildContext context) {
    printLog(
        'SSSSS ${widget.selectedColor?.value?.isEmpty} afffffafaggg ${widget.colors.firstOrNull?.stock}');
    if (widget.selectedColor?.value.isEmpty ?? true) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.setState(() {
          var firstAvailableColor =
              widget.colors.firstWhereOrNull((color) => color.stock != 0
                  // color.stock != null && color.stock! > 0,
                  );
          // printLog('AAVVVVVVVV ${firstAvailableColor}');

          if (firstAvailableColor != null) {
            widget.selectedColor?.value = firstAvailableColor.name;
          }
        });
      });

      //WidgetsBinding.instance.addPostFrameCallback((_) {
      //         widget.setState(() {
      //           widget.selectedColor?.value = widget.colors.first.name;
      //         });
      //       });
    }

    return Wrap(
      children: widget.colors.indexed.map((indexedColors) {
        final color = indexedColors.$2;

        final colorIsSelected = widget.selectedColor?.value == color.name;

        final border = colorIsSelected
            ? Border.all(
                color: color.name == whiteColor || color.name == blackColor
                    ? Colors.blueGrey.shade100
                    : Color(int.tryParse(color.name) ?? 0),
              )
            : null;

        void onSelectColor() {
          if (color.isOutOfStock) {
            Tools.showSnackBar(
                ScaffoldMessenger.of(context),
                isEnglish(context)
                    ? 'This color is out of stock'
                    : 'هذا اللون غير متوفر');
            return;
          }

          widget.setState(() {
            widget.selectedColor?.value = color.name;
          });
        }

        return Padding(
          padding: const EdgeInsets.all(4),
          child: Wrap(
            children: [
              GestureDetector(
                onTap: onSelectColor,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        border: border,
                        shape: BoxShape.circle,
                      ),
                      child: ChooseColorCard(
                        color: color.name,
                        isOutOffStock: color.isOutOfStock,
                      ),
                    ),
                    if (colorIsSelected)
                      //! Check
                      Icon(
                        Icons.check,
                        color:
                            color.name == whiteColor || color.name == blackColor
                                ? Colors.blueGrey.shade100
                                : Colors.white,
                        size: 17,
                      )
                  ],
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
