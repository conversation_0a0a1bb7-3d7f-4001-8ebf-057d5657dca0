import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../common/tools/tools.dart';
import '../../../common/tools/price_tools.dart';
import '../../../models/vendor/extra_setting_model.dart';
import '../../../models/index.dart';
import '../../checkout/widgets/convet_city_lang.dart';

class ProductExtrasList extends StatelessWidget {
  final List<ExtraSettingsModel> extras;
  final ValueNotifier<List<ExtraSettingsModel>>? selectedExtras;
  final Function setState;

  const ProductExtrasList({
    super.key,
    required this.selectedExtras,
    required this.extras,
    required this.setState,
  });

  @override
  Widget build(BuildContext context) {
    final currency = Provider.of<AppModel>(context, listen: false).currency;
    final currencyRate =
        Provider.of<AppModel>(context, listen: false).currencyRate;
    final primaryColor = Theme.of(context).primaryColor;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isEnglish(context) ? 'Extras' : 'الإضافات',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 8),
        ...extras.map((extra) {
          final isExtraSelected = selectedExtras?.value
                  .any((selectedExtra) => selectedExtra.id == extra.id) ??
              false;

          void onSelectExtra() {
            setState(() {
              final currentExtras =
                  List<ExtraSettingsModel>.from(selectedExtras?.value ?? []);

              if (isExtraSelected) {
                // Remove the extra if already selected
                currentExtras.removeWhere(
                    (selectedExtra) => selectedExtra.id == extra.id);
              } else {
                // Add the extra if not selected
                currentExtras.add(extra);
              }

              selectedExtras?.value = currentExtras;
            });
          }

          return ListTile(
            contentPadding: EdgeInsets.zero,
            title: Text(
              extra.name,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (extra.price != null && extra.price! > 0) ...[
                  Text(
                    '(+${PriceTools.getCurrencyFormatted(extra.price.toString(), currencyRate, currency: currency)})',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                Checkbox(
                  value: isExtraSelected,
                  activeColor: primaryColor,
                  onChanged: (_) => onSelectExtra(),
                ),
              ],
            ),
            onTap: onSelectExtra,
          );
        }).toList(),
      ],
    );
  }
}
