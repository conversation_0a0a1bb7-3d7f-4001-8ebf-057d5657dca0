import 'package:flutter/material.dart';
import '../../../data/boxes.dart';

class ChooseSizeCard extends StatelessWidget {
  final String size;
  final bool isSelected;
  final bool fromCart;
  final bool isOutOffStock;

  const ChooseSizeCard(
      {super.key,
      required this.size,
      this.isSelected = false,
      this.isOutOffStock = false,
      this.fromCart = false});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (size.isEmpty && fromCart) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: fromCart ? 14 : 16, vertical: fromCart ? 8 : 12),
      margin: const EdgeInsets.symmetric(
        vertical: 4,
      ),
      decoration: BoxDecoration(
          color: isSelected ? theme.primaryColor : Colors.transparent,
          border: isOutOffStock
              ? Border.fromBorderSide(
                  BorderSide(color: Colors.grey.withOpacity(0.3)),
                )
              : Border.fromBorderSide(
                  BorderSide(
                      color: isSelected ? Colors.transparent : Colors.grey),
                ),
          borderRadius: const BorderRadius.all(Radius.circular(8))),
      child: Text(
        size,
        style: TextStyle(
          color: isOutOffStock
              ? Colors.grey
              : isSelected || SettingsBox().isDarkTheme == true
                  ? Colors.white
                  : Colors.black,
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
