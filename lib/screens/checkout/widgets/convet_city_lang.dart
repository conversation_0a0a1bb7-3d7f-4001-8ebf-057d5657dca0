import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../models/app_model.dart';

bool isEnglish(
  BuildContext context,
) {
  var langCode = Provider.of<AppModel>(context, listen: false).langCode;

  final isEnglish = langCode == 'en';

  return isEnglish;
}

String cityLangToAr(String? enCityName) {
  enCityName = enCityName?.toLowerCase();
  switch (enCityName) {
    case 'alexandria':
      return 'الإسكندرية';
    case 'aswan':
      return 'أسوان';
    case 'asyut':
      return 'أسيوط';
    case 'beheira':
      return 'البحيرة';
    case 'beni suef':
      return 'بني سويف';
    case 'cairo':
      return 'القاهرة';
    case 'dakahlia':
      return 'الدقهلية';
    case 'damietta':
      return 'دمياط';
    case 'fayoum':
      return 'الفيوم';
    case 'gharbia':
      return 'الغربية';
    case 'giza':
      return 'الجيزة';
    case 'ismailia':
      return 'الإسماعيلية';
    case 'kafr el-sheikh':
      return 'كفر الشيخ';
    case 'matrouh':
      return 'مطروح';
    case 'minya':
      return 'المنيا';
    case 'monufia':
      return 'المنوفية';
    case 'el wadi el gedid':
      return 'الوادي الجديد';
    case 'north sinai':
      return 'شمال سيناء';
    case 'port said':
      return 'بورسعيد';
    case 'qalyubia':
      return 'القليوبية';
    case 'qena':
      return 'قنا';
    case 'red sea':
      return 'البحر الأحمر';
    case 'sharqia':
      return 'الشرقية';
    case 'sohag':
      return 'سوهاج';
    case 'south sinai':
      return 'جنوب سيناء';
    case 'suez':
      return 'السويس';
    case 'luxor':
      return 'الأقصر';
    default:
      return enCityName ?? 'Unknown';
  }
}
