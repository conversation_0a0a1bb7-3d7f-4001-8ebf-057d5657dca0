part of 'shipping_address.dart';

ValueNotifier<List<CountryState>> areas = ValueNotifier([]);
ValueNotifier<CountryState?> selectedArea = ValueNotifier(null);

extension on _ShippingAddressState {
  void updateAddress(Address? newAddress) {
    userShippingAddress = newAddress;
    loadUserInfoFromAddress(newAddress);
    loadAddressFields(userShippingAddress);
  }

  void loadUserInfoFromAddress(Address? address) {
    _textControllers[AddressFieldType.firstName]?.text =
        address?.firstName?.trim() ?? '';
    _textControllers[AddressFieldType.lastName]?.text =
        address?.lastName?.trim() ?? '';

    // Load merged name field
    final firstName = address?.firstName?.trim() ?? '';
    final lastName = address?.lastName?.trim() ?? '';
    _nameController.text = '$firstName $lastName'.trim();

    //TODO-Address-PhoneNumber
    _textControllers[AddressFieldType.phoneNumber]?.text =
        UserBox().userInfo?.phoneNumber?.trim() ??
            address?.phoneNumber?.trim() ??
            '';
    _textControllers[AddressFieldType.email]?.text =
        address?.email?.trim() ?? '';
  }

  void loadAddressFields(Address? address) {
    _textControllers[AddressFieldType.country]?.text =
        address?.country?.trim() ?? '';
    _textControllers[AddressFieldType.state]?.text =
        address?.stateId?.trim() ?? '';
    _textControllers[AddressFieldType.city]?.text = address?.city?.trim() ?? '';
    _textControllers[AddressFieldType.apartment]?.text =
        address?.apartment?.trim() ?? '';
    _textControllers[AddressFieldType.block]?.text =
        address?.block?.trim() ?? '';
    _textControllers[AddressFieldType.street]?.text =
        address?.street?.trim() ?? '';
    _textControllers[AddressFieldType.zipCode]?.text =
        address?.zipCode?.trim() ?? '';
    refresh();
  }

  // Helper method to split the merged name into firstName and lastName
  void _splitNameField() {
    final fullName = _nameController.text.trim();
    if (fullName.isNotEmpty) {
      final nameParts = fullName.split(' ');
      if (nameParts.length >= 2) {
        _textControllers[AddressFieldType.firstName]?.text = nameParts.first;
        _textControllers[AddressFieldType.lastName]?.text =
            nameParts.skip(1).join(' ');
      } else {
        _textControllers[AddressFieldType.firstName]?.text = fullName;
        _textControllers[AddressFieldType.lastName]?.text = '';
      }
    } else {
      _textControllers[AddressFieldType.firstName]?.text = '';
      _textControllers[AddressFieldType.lastName]?.text = '';
    }
  }

  // Helper method to check if a field is required based on checkout settings
  bool _isFieldRequired(AddressFieldType fieldType) {
    final checkoutSettings = currentVendor?.config?.checkoutSettings;
    if (checkoutSettings == null) {
      return true; // Default to required if no settings
    }

    switch (fieldType) {
      case AddressFieldType.firstName:
      case AddressFieldType.lastName:
        return checkoutSettings.name.isRequired;
      case AddressFieldType.email:
        return checkoutSettings.email.isRequired;
      case AddressFieldType.phoneNumber:
        return checkoutSettings.phone.isRequired;
      default:
        return true; // Require other fields by default
    }
  }

  bool checkToSave() {
    var listAddress = <Address>[];
    var data = UserBox().addresses;
    if (data.isNotEmpty) {
      listAddress.addAll(data);
    }
    for (var local in listAddress) {
      final isNotExistedInLocal = local.isDiff(
        Address(
          city: _textControllers[AddressFieldType.city]?.text,
          street: _textControllers[AddressFieldType.street]?.text,
          zipCode: _textControllers[AddressFieldType.zipCode]?.text,
          stateId: _textControllers[AddressFieldType.state]?.text,
        ),
      );

      if (isNotExistedInLocal) {
        continue;
      }

      showDialog(
        context: context,
        useRootNavigator: false,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text(S.of(context).yourAddressExistYourLocal),
            actions: <Widget>[
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(
                  S.of(context).ok,
                  style: TextStyle(color: Theme.of(context).primaryColor),
                ),
              )
            ],
          );
        },
      );
      return false;
    }
    return true;
  }

  void saveDataToLocal() {
    var listAddress = <Address>[];
    final address = userShippingAddress;
    if (address != null) {
      listAddress.add(address);
    }
    var listData = UserBox().addresses;
    if (listData.isNotEmpty) {
      for (var item in listData) {
        listAddress.add(item);
      }
    }
    UserBox().addresses = listAddress;
    FlashHelper.message(
      context,
      message: S.of(context).yourAddressHasBeenSaved,
    );
  }

  String? validateEmail(String email) {
    if (email.isEmail) {
      return null;
    }
    return 'The E-mail Address must be a valid email address.';
  }

  /// Load Shipping beforehand
  void _loadShipping({bool beforehand = true}) {
    Services().widget.loadShippingMethods(
        context, Provider.of<CartModel>(context, listen: false), beforehand);
  }

  /// on tap to Next Button
  void _onNext() {
    {
      final isAreasNotEmptyButSelectedAreaIsNull =
          (areas.value.isNotEmpty && selectedArea.value == null);

      if (_formKey.currentState!.validate()) {
        if (isAreasNotEmptyButSelectedAreaIsNull) {
          FlashHelper.errorMessage(
            context,
            message: S.of(context).pleaseInput,
          );
          return;
        }

        _formKey.currentState!.save();
        Provider.of<CartModel>(context, listen: false)
            .setAddress(userShippingAddress);
        _loadShipping(beforehand: false);

        final userPickedLocationFromMap =
            GetStorageService.hasData(key: LocalKeys.location) == true;

        final stateId = userShippingAddress!.stateId;

        if (stateId == null || stateId.isEmpty) {
          FlashHelper.errorMessage(
            context,
            message: S.of(context).pleaseChooseValidCity,
          );
        } else if (currentVendor?.config?.showMap == true &&
            !userPickedLocationFromMap) {
          FlashHelper.errorMessage(
            context,
            message: S.of(context).pleasePickYourLocationFromMap,
          );
        } else {
          widget.onNext!();
        }
      } else {
        FlashHelper.errorMessage(
          context,
          message: S.of(context).pleaseInput,
        );
      }
    }
  }

  Widget renderStateInput(bool isDesktop) {
    var items = <DropdownMenuItem>[];
    for (var item in citiesList!) {
      if (item.isActive == false) {
        continue;
      }
      items.add(
        DropdownMenuItem(
          value: item.id,
          child: Text(HtmlUnescape().convert(isEnglish(context)
              ? (item.name ?? '')
              : cityLangToAr(item.name))),
        ),
      );
    }

    String? selectedCityId;
    CountryState? selectedState;

    Object? firstState = citiesList!.firstWhereOrNull(
        (o) => o.id.toString() == userShippingAddress!.stateId.toString());

    if (firstState != null) {
      selectedCityId = userShippingAddress!.stateId;
      selectedState = firstState as CountryState;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (isDesktop)
          Padding(
            padding: const EdgeInsets.only(bottom: 6.0),
            child: Text(
              S.of(context).stateProvince,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
                height: 20 / 14,
              ),
            ),
          ),
        Container(
          // height: isDesktop ? (51) : null,
          // decoration: isDesktop
          //     ? BoxDecoration(
          //         border: Border.all(
          //           width: 1,
          //           color: Colors.grey[400]!,
          //         ),
          //         borderRadius: BorderRadius.circular(4),
          //         color: Theme.of(context).colorScheme.surface,
          //       )
          //     : null,
          padding: isDesktop
              ? EdgeInsets.zero
              // EdgeInsets.all(isEnglish(context) ? 16 : 8)
              : const EdgeInsets.only(top: 12),
          child: BaseSearchSheet(
            data: citiesList!,
            selectedValue: selectedState,
            itemModelAsName: (item) => context.isEng
                ? (item as CountryState?)?.name ?? item?.nameAr
                : (item as CountryState?)?.nameAr ?? item?.name,
            onChanged: (countryData) async {
              final val = (countryData as CountryState?)?.id ?? '';
              selectedCityId = val;

              //TODO-Cities-OnChanged
              selectedArea.value = null;
              refresh();

              userShippingAddress!.stateId = val;

              areas.value = citiesList
                      ?.firstWhereOrNull(
                        (o) =>
                            o.id.toString() ==
                            userShippingAddress!.stateId.toString(),
                      )
                      ?.areas
                      ?.map((e) => e.copyWith(city: countryData))
                      .toList() ??
                  [];

              selectedArea.value = areas.value.firstOrNull;

              ShippingMethodModel.selectedShippingMethodCost.value = 0.0;

              userShippingAddress?.setStateName(
                states: citiesList ?? [],
                stateId: val,
              );

              final country = Country(id: userShippingAddress!.country);
              final state = CountryState(id: val);
              areasList = await Services().widget.loadCities(country, state);

              final isFreeShipping =
                  (selectedArea.value?.freeShipping ?? false) ||
                      (citiesList
                              ?.firstWhereOrNull(
                                (o) =>
                                    o.id.toString() ==
                                    userShippingAddress!.stateId.toString(),
                              )
                              ?.freeShipping ??
                          false);

              // set shipping price
              ShippingMethodModel.selectedShippingMethodCost.value =
                  isFreeShipping
                      ? 0.0
                      : selectedArea.value?.cost?.toDouble() ??
                          citiesList
                              ?.firstWhereOrNull(
                                (o) =>
                                    o.id.toString() ==
                                    userShippingAddress!.stateId.toString(),
                              )
                              ?.cost
                              ?.toDouble() ??
                          0.0;

              printLog(
                  'COSTTTTT ${ShippingMethodModel.selectedShippingMethodCost.value}');

              // address!.zipCode = '';
              // _textControllers[AddressFieldType.zipCode]?.text = '';
              refresh();
            },
            label: S.of(context).stateProvince,
          ),

          // DropdownButton(
          //   items: items,
          //   value: value,
          //   borderRadius: BorderRadius.circular(12),
          //   onChanged: (dynamic val) async {
          //     //TODO-Cities-OnChanged
          //     selectedArea.value = null;
          //     refresh();
          //
          //     address!.stateId = val;
          //
          //     areas.value = states
          //             ?.firstWhereOrNull(
          //               (o) => o.id.toString() == address!.stateId.toString(),
          //             )
          //             ?.areas ??
          //         [];
          //
          //     selectedArea.value = areas.value.firstOrNull;
          //
          //     ShippingMethodModel.selectedShippingMethodCost.value = 0.0;
          //
          //     address?.setStateName(
          //       states: states ?? [],
          //       stateId: val,
          //     );
          //
          //     final country = Country(id: address!.country);
          //     final state = CountryState(id: val);
          //     cities = await Services().widget.loadCities(country, state);
          //
          //     // address!.zipCode = '';
          //     // _textControllers[AddressFieldType.zipCode]?.text = '';
          //     refresh();
          //   },
          //   isExpanded: true,
          //   itemHeight: 70,
          //   icon:
          //       isDesktop ? const Icon(Icons.arrow_drop_down, size: 20) : null,
          //   hint: Text(S.of(context).stateProvince),
          //   underline: isDesktop ? const SizedBox() : null,
          // ),
        ),
      ],
    );
  }

  Widget renderCityInput(int index) {
    var items = <DropdownMenuItem>[];
    for (var item in areasList!) {
      items.add(
        DropdownMenuItem(
          value: item.id,
          child: Text(item.name!),
        ),
      );
    }
    String? value;

    Object? firstCity = areasList!.firstWhereOrNull(
        (o) => o.name.toString() == userShippingAddress!.city.toString());

    if (firstCity != null) {
      value = userShippingAddress!.city;
    }
    return DropdownButtonFormField<dynamic>(
      items: items,
      value: value,
      validator: (val) {
        final config = _configs[index];
        if (config == null) {
          return null;
        }
        return validateField(
            val, config, _fieldPosition[index] ?? AddressFieldType.unknown);
      },
      onChanged: (dynamic val) async {
        userShippingAddress!.city = val;
        final country = Country(id: userShippingAddress!.country);
        final state = CountryState(id: userShippingAddress!.stateId);
        final city = City(id: val, name: val);
        final zipCode =
            await Services().widget.loadZipCode(country, state, city);
        if (zipCode != null) {
          userShippingAddress!.zipCode = zipCode;
          _textControllers[AddressFieldType.zipCode]?.text = zipCode;
        }
        refresh();
      },
      isExpanded: true,
      itemHeight: 70,
      hint: Text(S.of(context).city),
    );
  }

  void _openCountryPickerDialog() => showDialog(
        context: context,
        useRootNavigator: false,
        builder: (contextBuilder) => countries!.isEmpty
            ? Theme(
                data: Theme.of(context).copyWith(primaryColor: Colors.pink),
                child: SizedBox(
                  height: 500,
                  child: picker.CountryPickerDialog(
                    titlePadding: const EdgeInsets.all(8.0),
                    contentPadding: const EdgeInsets.all(2.0),
                    searchCursorColor: Colors.pinkAccent,
                    searchInputDecoration:
                        const InputDecoration(hintText: 'Search...'),
                    isSearchable: true,
                    title: Text(S.of(context).country),
                    onValuePicked: (picker_country.Country country) async {
                      _textControllers[AddressFieldType.country]?.text =
                          country.isoCode;
                      userShippingAddress!.country = country.isoCode;
                      refresh();
                      final c =
                          Country(id: country.isoCode, name: country.name);
                      citiesList = await Services().widget.loadStates(c);
                      userShippingAddress!.zipCode = '';
                      _textControllers[AddressFieldType.zipCode]?.text = '';
                      refresh();
                    },
                    itemBuilder: (country) {
                      return Row(
                        children: <Widget>[
                          picker.CountryPickerUtils.getDefaultFlagImage(
                              country),
                          const SizedBox(width: 8.0),
                          Expanded(child: Text(country.name)),
                        ],
                      );
                    },
                  ),
                ),
              )
            : Dialog(
                child: CountrySelectorWidget(
                  countries: countries,
                  onTap: (Country country) async {
                    _textControllers[AddressFieldType.country]?.text =
                        country.code!;
                    userShippingAddress!.country = country.id;
                    userShippingAddress!.countryId = country.id;
                    refresh();
                    Navigator.pop(contextBuilder);
                    citiesList = await Services().widget.loadStates(country);
                    userShippingAddress!.zipCode = '';
                    _textControllers[AddressFieldType.zipCode]?.text = '';
                    refresh();
                  },
                ),
              ),
      );

  void onTextFieldSaved(String? value, AddressFieldType type) {
    switch (type) {
      case AddressFieldType.firstName:
        userShippingAddress?.firstName = value;
        // Also update the merged name field if this is being set directly
        if (userShippingAddress?.lastName != null) {
          _nameController.text =
              '${value ?? ''} ${userShippingAddress!.lastName!}'.trim();
        }
        break;
      case AddressFieldType.lastName:
        userShippingAddress?.lastName = value;
        // Also update the merged name field if this is being set directly
        if (userShippingAddress?.firstName != null) {
          _nameController.text =
              '${userShippingAddress!.firstName!} ${value ?? ''}'.trim();
        }
        break;
      case AddressFieldType.phoneNumber:
        userShippingAddress?.phoneNumber = value;
        break;
      case AddressFieldType.email:
        userShippingAddress?.email = value;
        break;
      case AddressFieldType.country:
        userShippingAddress?.country = value;
        break;
      case AddressFieldType.state:
        userShippingAddress?.stateId = value;
        break;
      case AddressFieldType.city:
        userShippingAddress?.city = value;
        break;
      case AddressFieldType.apartment:
        userShippingAddress?.apartment = value;
        break;
      case AddressFieldType.block:
        userShippingAddress?.block = value;
        break;
      case AddressFieldType.street:
        userShippingAddress?.street = value;
        break;
      case AddressFieldType.zipCode:
        userShippingAddress?.zipCode = value?.trim();
        break;

      /// Unsupported.
      case AddressFieldType.searchAddress:
      case AddressFieldType.selectAddress:
      case AddressFieldType.unknown:
      default:
        break;
    }
  }

  String? validateField(
      String? val, AddressFieldConfig config, AddressFieldType type) {
    // Check if field is required based on checkout settings
    final isRequired = _isFieldRequired(type);
    if (!isRequired && !config.required) {
      return null;
    }

    final label = type.getTitle(context)?.toLowerCase();
    if ((val?.isEmpty ?? true) &&
        label != null &&
        (isRequired || config.required)) {
      return S.of(context).theFieldIsRequired(label);
    }
    if (val != null && type == AddressFieldType.email) {
      return validateEmail(val);
    }
    return null;
  }

  TextInputType getKeyboardType(AddressFieldType type) {
    if (type == AddressFieldType.zipCode &&
        kPaymentConfig.enableAlphanumericZipCode) {
      return TextInputType.streetAddress;
    }
    return type.keyboardType;
  }

  Widget _buildBottom(bool isDesktop) {
    // Hide buttons if hideButtons is true
    if (widget.hideButtons) {
      return const SizedBox.shrink();
    }

    final bgButton = Theme.of(context).primaryColor;

    final btnContinue = ElevatedButton.icon(
      style: ElevatedButton.styleFrom(
        foregroundColor: Colors.white,
        backgroundColor: bgButton,
        elevation: 0.0,
        padding: const EdgeInsets.symmetric(horizontal: 16),
      ),
      icon: Icon(
        Icons.local_shipping_outlined,
        size: 18,
        color: bgButton.getColorBasedOnBackground,
      ),
      onPressed: _onNext,
      label: FittedBox(
        fit: BoxFit.scaleDown,
        child: Text(
          (kPaymentConfig.enableShipping
                  ? S.of(context).continueToShipping
                  : kPaymentConfig.enableReview
                      ? S.of(context).continueToReview
                      : S.of(context).continueToPayment)
              .toUpperCase(),
          style: Theme.of(context)
              .textTheme
              .bodySmall!
              .copyWith(color: bgButton.getColorBasedOnBackground),
        ),
      ),
    );

    return CommonSafeArea(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 150,
            child: OutlinedButton.icon(
              style: OutlinedButton.styleFrom(padding: EdgeInsets.zero),
              onPressed: () {
                if (!checkToSave()) return;
                if (_formKey.currentState!.validate()) {
                  _formKey.currentState!.save();
                  Provider.of<CartModel>(context, listen: false)
                      .setAddress(userShippingAddress);
                  saveDataToLocal();
                } else {
                  FlashHelper.errorMessage(
                    context,
                    message: S.of(context).pleaseInput,
                  );
                }
              },
              icon: const Icon(
                CupertinoIcons.plus_app,
                size: 20,
              ),
              label: FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  S.of(context).saveAddress.toUpperCase(),
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        color: Theme.of(context).colorScheme.secondary,
                      ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          if (isDesktop) btnContinue else Expanded(child: btnContinue),
        ],
      ),
    );
  }

  bool isFieldReadOnly(int index) {
    final config = _configs[index];
    if (config == null) {
      return false;
    }

    /// Disable edit only when the field has a default value.
    if (!config.editable && config.defaultValue.isNotEmpty) {
      return true;
    }

    return false;
  }

  Widget _renderSelectAddressButton() => GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: _onShowSelectAddressForDesktop,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                CupertinoIcons.person_crop_square,
                size: 16,
                color: Colors.blue[800],
              ),
              const SizedBox(width: 4),
              Text(
                S.of(context).selectAddress,
                style: TextStyle(
                  color: Colors.blue[800],
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      );
}

class CountrySelectorWidget extends StatefulWidget {
  const CountrySelectorWidget({
    super.key,
    this.countries,
    required this.onTap,
  });

  final List<Country>? countries;
  final void Function(Country) onTap;

  @override
  State<CountrySelectorWidget> createState() => _CountrySelectorWidgetState();
}

class _CountrySelectorWidgetState extends State<CountrySelectorWidget> {
  late final _listCountry = List<Country>.from(widget.countries ?? []);
  late final _listCountryShow = List<Country>.from(_listCountry);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.sizeOf(context).height * 0.7,
      constraints: const BoxConstraints(
        maxWidth: 600,
      ),
      padding: const EdgeInsets.all(10),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              S.of(context).country,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          TextFormField(
            decoration: InputDecoration(
              hintText: S.of(context).country,
            ),
            onChanged: (value) {
              setState(() {
                final listItem = List<Country>.from(_listCountry);
                if (value.isNotEmpty) {
                  listItem.removeWhere((element) {
                    final valueCompare = value.toLowerCase();
                    final notContainsName =
                        element.name!.toLowerCase().contains(valueCompare) ==
                            false;

                    final notContainsIsoCode =
                        element.code!.toLowerCase().contains(valueCompare) ==
                            false;

                    return notContainsIsoCode && notContainsName;
                  });
                }

                _listCountryShow
                  ..clear()
                  ..addAll(listItem);
              });
            },
          ),
          const SizedBox(height: 5),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: List.generate(
                  _listCountryShow.length,
                  (index) {
                    return GestureDetector(
                      onTap: () async {
                        widget.onTap(_listCountryShow[index]);
                      },
                      child: ListTile(
                        contentPadding: EdgeInsets.zero,
                        leading: _listCountryShow[index].icon != null
                            ? SizedBox(
                                height: 30,
                                width: 50,
                                child: FluxImage(
                                  imageUrl: _listCountryShow[index].icon!,
                                  fit: BoxFit.cover,
                                ),
                              )
                            : (_listCountryShow[index].code != null
                                ? Image.asset(
                                    picker.CountryPickerUtils
                                        .getFlagImageAssetPath(
                                            _listCountryShow[index].code!),
                                    height: 30,
                                    width: 50,
                                    fit: BoxFit.fill,
                                    package: 'country_pickers',
                                  )
                                : const SizedBox(
                                    height: 30,
                                    width: 50,
                                    child: Icon(Icons.streetview),
                                  )),
                        title: Text(_listCountryShow[index].name!),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
