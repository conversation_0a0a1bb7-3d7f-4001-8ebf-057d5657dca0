import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:collection/collection.dart';

import '../../common/config.dart';
import '../../common/config/models/cart_config.dart';
import '../../common/constants.dart';
import '../../common/constants/local_keys.dart';
import '../../common/tools/flash.dart';
import '../../generated/l10n.dart';
import '../../models/index.dart'
    show CartModel, Order, PaymentMethodModel, Address;
import '../../models/vendor/checkout_settings_model.dart' show CheckoutType;
import '../../models/shipping_method_model.dart';
import '../../models/entities/country_state.dart';
import '../../modules/analytics/analytics.dart';
import '../../modules/dynamic_layout/helper/helper.dart';
import '../../services/get_storage_service.dart';
import '../../services/index.dart';
import '../../widgets/product/product_bottom_sheet.dart';
import '../../widgets/web_layout/web_layout.dart';
import '../base_screen.dart';
import '../cart/my_cart_layout/my_cart_normal_layout_web.dart';
import 'mixins/checkout_mixin.dart';
import 'review_screen.dart';
import 'widgets/payment_methods.dart';
import 'widgets/shipping_address.dart';
import 'widgets/stepper_checkout_widet.dart';
import 'widgets/success.dart';
import 'package:flutter/cupertino.dart';

class CheckoutArgument {
  final bool? isModal;

  const CheckoutArgument({this.isModal});
}

class Checkout extends StatefulWidget {
  final bool? isModal;

  const Checkout({this.isModal});

  @override
  BaseScreen<Checkout> createState() => _CheckoutState();
}

class _CheckoutState extends BaseScreen<Checkout> with CheckoutMixin {
  int tabIndex = 0;
  Order? newOrder;
  bool isPayment = false;
  bool isLoading = false;
  bool isLoadingShippingCost = true;
  bool enabledShipping = kPaymentConfig.enableShipping;
  bool isEditingAddress = false;

  bool get isDesktop => Layout.isDisplayDesktop(context);

  final shippingFormKey = GlobalKey<FormState>();

  // Required getters from CheckoutMixin
  @override
  Function? get onBack => () {
        if (kPaymentConfig.enableReview) {
          goToReviewTab(true);
        } else {
          goToAddressTab(true);
        }
      };

  @override
  Function? get onFinish => (Order? order) {
        setState(() {
          newOrder = order;
        });
      };

  @override
  Function(bool)? get onLoading => (bool loading) {
        setState(() {
          isLoading = loading;
        });
      };

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      final cartModel = Provider.of<CartModel>(context, listen: false);
      Analytics.triggerBeginCheckout(context);
      setState(() {
        enabledShipping = cartModel.isEnabledShipping();
      });
    });
  }

  void setLoading(bool loading) {
    setState(() {
      isLoading = loading;
    });
  }

  OrderSummaryStyle get summaryStyle {
    if (isDesktop) {
      switch (tabIndex) {
        case 0:
          return OrderSummaryStyle.webCheckoutShippingAddress;
        case 1:
          return OrderSummaryStyle.webCheckoutShippingMethod;
        case 2:
          return OrderSummaryStyle.webCheckoutReview;
        case 3:
          return OrderSummaryStyle.webCheckoutPayment;
        default:
          return OrderSummaryStyle.normal;
      }
    }
    return OrderSummaryStyle.normal;
  }

  @override
  void afterFirstLayout(BuildContext context) {
    // Auto-select first shipping method since shipping tab is hidden
    _autoSelectShippingMethod();

    // Set initial tab based on enabled features
    if (!kPaymentConfig.enableAddress) {
      // If address is disabled, go to review or payment
      setState(() {
        tabIndex = kPaymentConfig.enableReview ? 1 : 2;
        if (!kPaymentConfig.enableReview) {
          isPayment = true;
        }
      });
    } else {
      // Start with address tab (index 0)
      setState(() {
        tabIndex = 0;
      });
    }
  }

  // Auto-select the first available shipping method
  void _autoSelectShippingMethod() async {
    try {
      final cartModel = Provider.of<CartModel>(context, listen: false);
      final shippingMethods = await Services().api.getShippingMethods(
            cartModel: cartModel,
          );

      if (shippingMethods?.isNotEmpty ?? false) {
        await cartModel.setShippingMethod(shippingMethods!.first);
      }
    } catch (e) {
      printLog('Error auto-selecting shipping method: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget progressBar = Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: <Widget>[
        kPaymentConfig.enableAddress
            ? Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      tabIndex = 0;
                    });
                  },
                  child: Column(
                    children: <Widget>[
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 13),
                        child: Text(
                          S.of(context).address.toUpperCase(),
                          style: TextStyle(
                              color: tabIndex == 0
                                  ? Theme.of(context).primaryColor
                                  : Theme.of(context).colorScheme.secondary,
                              fontSize: 12,
                              fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      tabIndex >= 0
                          ? ClipRRect(
                              borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(2.0),
                                  bottomLeft: Radius.circular(2.0)),
                              child: Container(
                                  height: 3.0,
                                  color: Theme.of(context).primaryColor),
                            )
                          : Divider(
                              height: 2,
                              color: Theme.of(context).colorScheme.secondary)
                    ],
                  ),
                ),
              )
            : const SizedBox(),
        false // Hide shipping tab
            ? Expanded(
                child: GestureDetector(
                  onTap: () {
                    // if (cartModel.address != null &&
                    //     cartModel.address!.isValid()) {
                    //   setState(() {
                    //     tabIndex = 1;
                    //   });
                    // }
                  },
                  child: Column(
                    children: <Widget>[
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 13),
                        child: Text(
                          S.of(context).shipping.toUpperCase(),
                          style: TextStyle(
                              color: tabIndex == 1
                                  ? Theme.of(context).primaryColor
                                  : Theme.of(context).colorScheme.secondary,
                              fontSize: 12,
                              fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      tabIndex >= 1
                          ? Container(
                              height: 3.0,
                              color: Theme.of(context).primaryColor)
                          : Divider(
                              height: 2,
                              color: Theme.of(context).colorScheme.secondary)
                    ],
                  ),
                ),
              )
            : const SizedBox(),
        kPaymentConfig.enableReview
            ? Expanded(
                child: GestureDetector(
                  onTap: () {
                    // if (cartModel.shippingMethod != null) {
                    //   setState(() {
                    //     tabIndex = 2;
                    //   });
                    // }
                  },
                  child: Column(
                    children: <Widget>[
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 13),
                        child: Text(
                          S.of(context).preview.toUpperCase(),
                          style: TextStyle(
                            color: tabIndex == 1
                                ? Theme.of(context).primaryColor
                                : Theme.of(context).colorScheme.secondary,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      tabIndex >= 1
                          ? Container(
                              height: 3.0,
                              color: Theme.of(context).primaryColor)
                          : Divider(
                              height: 2,
                              color: Theme.of(context).colorScheme.secondary)
                    ],
                  ),
                ),
              )
            : const SizedBox(),
        Expanded(
          child: GestureDetector(
            onTap: () {
              // if (cartModel.shippingMethod != null) {
              //   setState(() {
              //     tabIndex = 3;
              //   });
              // }
            },
            child: Column(
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 13),
                  child: Text(
                    S.of(context).payment.toUpperCase(),
                    style: TextStyle(
                      color: tabIndex == 2
                          ? Theme.of(context).primaryColor
                          : Theme.of(context).colorScheme.secondary,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                tabIndex >= 2
                    ? ClipRRect(
                        borderRadius: const BorderRadius.only(
                            topRight: Radius.circular(2.0),
                            bottomRight: Radius.circular(2.0)),
                        child: Container(
                            height: 3.0, color: Theme.of(context).primaryColor),
                      )
                    : Divider(
                        height: 2,
                        color: Theme.of(context).colorScheme.secondary)
              ],
            ),
          ),
        )
      ],
    );
    var body = newOrder != null
        ? OrderedSuccess(
            order: newOrder,
            hasScroll: isDesktop == false,
          )
        : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              // Hide progress bar for one-page checkout
              (isDesktop == false &&
                      currentVendor?.config?.checkoutSettings?.checkoutType !=
                          CheckoutType.onePage)
                  ? progressBar
                  : const SizedBox(),
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: renderContent(),
                    ),
                    if (isDesktop)
                      Padding(
                        padding: const EdgeInsetsDirectional.only(start: 30),
                        child: Column(
                          children: [
                            Align(
                              alignment: Alignment.topCenter,
                              child: Padding(
                                padding: const EdgeInsets.only(top: 20),
                                child: Container(
                                  width: 400,
                                  decoration: BoxDecoration(
                                    color:
                                        Theme.of(context).colorScheme.surface,
                                    borderRadius: BorderRadius.circular(4),
                                    boxShadow: [
                                      BoxShadow(
                                        blurRadius: 5,
                                        color: kGrey400.withOpacity(0.15),
                                        spreadRadius: 5,
                                      )
                                    ],
                                  ),
                                  child: OrderSummaryWidget(
                                    errMsg: '',
                                    isModal: false,
                                    style: summaryStyle,
                                    onBack: _checkoutOnBack,
                                    onFinish: _checkoutOnFinish,
                                    onLoading: setLoading,
                                    onNext: () {
                                      if (tabIndex == 1 &&
                                          kPaymentConfig.enableReview) {
                                        goToPaymentTab();
                                      }
                                    },
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              )
            ],
          );

    if (isDesktop) {
      body = WillPopScope(
        onWillPop: () async {
          if (tabIndex > 0) {
            if (tabIndex == 1) {
              // From review tab, go back to address
              goToAddressTab(true);
            } else if (tabIndex == 2) {
              // From payment tab, go back to review (if enabled) or address
              if (kPaymentConfig.enableReview) {
                goToReviewTab(true);
              } else {
                goToAddressTab(true);
              }
            }
            return false;
          }
          return true;
        },
        child: SliverWebLayout(
          actionBuilder: () {
            return SizedBox(
              height: 60,
              child: Padding(
                padding: EdgeInsetsDirectional.only(
                    start: MediaQuery.sizeOf(context).width * 0.1),
                child: Align(
                  alignment: AlignmentDirectional.centerStart,
                  child: StepperCheckoutWidget(
                    currentStep: tabIndex,
                    items: [
                      StepperCheckoutItem(
                        index: 0,
                        title: S.of(context).address,
                      ),
                      StepperCheckoutItem(
                        index: 1,
                        title: S.of(context).shipping,
                      ),
                      StepperCheckoutItem(
                        index: 2,
                        title: S.of(context).review.toTitleCase(),
                      ),
                      StepperCheckoutItem(
                        index: 3,
                        title: S.of(context).payment,
                      )
                    ],
                    width: MediaQuery.sizeOf(context).width * 0.4,
                  ),
                ),
              ),
            );
          },
          slivers: [
            if (newOrder == null)
              SliverFillRemaining(
                hasScrollBody: false,
                child: LayoutLimitWidthScreen(child: body),
              )
            else
              SliverToBoxAdapter(
                child: LayoutLimitWidthScreen(child: body),
              ),
          ],
        ),
      );
    }

    return Stack(
      children: <Widget>[
        Scaffold(
          backgroundColor: Theme.of(context)
              .colorScheme
              .surface
              .withOpacity(isDesktop ? 0.1 : 1),
          appBar: isDesktop
              ? null
              : AppBar(
                  backgroundColor: Theme.of(context).colorScheme.surface,
                  title: Text(
                    S.of(context).checkout,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.secondary,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  // actions: <Widget>[
                  //   if (widget.isModal != null && widget.isModal == true)
                  //     IconButton(
                  //       icon: const Icon(Icons.close, size: 24),
                  //       onPressed: _onPressCloseButton,
                  //     ),
                  // ],
                ),
          body: SafeArea(bottom: false, child: body),
        ),
        isLoading
            ? Container(
                height: MediaQuery.of(context).size.height,
                width: MediaQuery.of(context).size.width,
                color: Colors.white.withOpacity(0.36),
                child: kLoadingWidget(context),
              )
            : const SizedBox()
      ],
    );
  }

  Widget renderContent() {
    // Check if we should use one-page checkout
    final checkoutType = currentVendor?.config?.checkoutSettings?.checkoutType;
    if (checkoutType == CheckoutType.onePage &&
        !Layout.isDisplayDesktop(context)) {
      return _buildOnePageCheckout();
    }

    // Original step-based checkout
    switch (tabIndex) {
      case 0:
        return SizedBox(
          key: const ValueKey(0),
          child: ShippingAddress(
              formKey: shippingFormKey,
              onNext: () {
                // Go to review tab if enabled, otherwise go to payment
                if (kPaymentConfig.enableReview) {
                  goToReviewTab();
                } else {
                  goToPaymentTab();
                }
              }),
        );
      case 1:
        // Review tab (only if enabled)
        if (kPaymentConfig.enableReview) {
          return SizedBox(
            key: const ValueKey(1),
            child: ReviewScreen(onBack: () {
              goToAddressTab(true);
            }, onNext: () {
              goToPaymentTab();
            }),
          );
        }
        // If review is disabled, fall through to payment
        return SizedBox(
          key: const ValueKey(1),
          child: PaymentMethods(
            hideCheckout: isDesktop,
            onBack: _checkoutOnBack,
            onFinish: _checkoutOnFinish,
            onLoading: setLoading,
          ),
        );
      case 2:
      default:
        return SizedBox(
          key: const ValueKey(2),
          child: PaymentMethods(
            hideCheckout: isDesktop,
            onBack: _checkoutOnBack,
            onFinish: _checkoutOnFinish,
            onLoading: setLoading,
          ),
        );
    }
  }

  // Build one-page checkout with fixed bottom button
  Widget _buildOnePageCheckout() {
    // Always use simple layout for one-page checkout
    return _buildSimpleOnePageCheckout();
  }

  // Build simple one-page checkout without expansion tiles
  Widget _buildSimpleOnePageCheckout() {
    // final cartModel = Provider.of<CartModel>(context);
    // final hasAddress = userShippingAddress != null;
    // && userShippingAddress!.isValid();

    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Address Section - unified section that handles both view and edit states
                _buildAddressSection(),

                const SizedBox(height: 16),

                // Order Summary Section
                _buildSimpleSection(
                  title: S.of(context).orderSummary,
                  icon: Icons.receipt_long_outlined,
                  child: ReviewScreen(
                    onBack: () {},
                    onNext: () {},
                    fromOnePageCheckout: true,
                  ),
                ),

                const SizedBox(height: 16),

                // Payment Section
                _buildSimpleSection(
                  title: S.of(context).paymentMethods,
                  icon: Icons.payment_outlined,
                  child: PaymentMethods(
                    hideCheckout: true,
                    fromOnePageCheckout: true,
                    onBack: () {},
                    onFinish: _checkoutOnFinish,
                    onLoading: setLoading,
                  ),
                ),
              ],
            ),
          ),
        ),
        _buildFixedCheckoutButton(),
      ],
    );
  }

  // Build simple section without expansion tiles
  Widget _buildSimpleSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Card(
      elevation: 1,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      clipBehavior: Clip.antiAlias,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.05),
              // border: Border(
              //   bottom: BorderSide(
              //     color: Theme.of(context).dividerColor.withOpacity(0.3),
              //     width: 1,
              //   ),
              // ),
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
          ),
          // Section content
          child,
        ],
      ),
    );
  }

  // Build unified address section that handles both view and edit states
  Widget _buildAddressSection() {
    final cartModel = Provider.of<CartModel>(context);
    final address = cartModel.address;
    final hasAddress = address != null;

    return Card(
      elevation: 1,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      clipBehavior: Clip.antiAlias,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header with edit/save button
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.05),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.location_on_outlined,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  S.of(context).shippingAddress,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                const Spacer(),
                InkWell(
                  borderRadius: BorderRadius.circular(100),
                  onTap: () async {
                    if (!hasAddress || isEditingAddress) {
                      // Save action (for new address or editing existing)
                      if (shippingFormKey.currentState?.validate() == true) {
                        // validate city
                        if (userShippingAddress?.city == null ||
                            userShippingAddress?.city?.isEmpty == true) {
                          FlashHelper.errorMessage(
                            context,
                            message: S.of(context).pleaseChooseValidCity,
                          );
                          return;
                        }

                        shippingFormKey.currentState!.save();

                        // Save the address to cart model
                        cartModel.setAddress(userShippingAddress);

                        // Calculate shipping cost for the saved address
                        if (userShippingAddress != null) {
                          _calculateShippingCostFromSavedAddress(
                              userShippingAddress!);
                        }

                        setState(() {
                          isEditingAddress = false;
                        });

                        // Show success message
                        FlashHelper.message(
                          context,
                          message: S.of(context).saveAddressSuccess,
                        );
                      }
                    } else {
                      // Edit action (when address exists and not editing)
                      setState(() {
                        isEditingAddress = true;
                      });
                    }
                  },
                  child: Icon(
                    (!hasAddress || isEditingAddress)
                        ? CupertinoIcons.checkmark_circle_fill
                        : CupertinoIcons.square_pencil,
                    color: Theme.of(context).primaryColor,
                    size: (!hasAddress || isEditingAddress) ? 26 : 20,
                  ),
                ),
              ],
            ),
          ),
          // Section content - either form or address display
          if (!hasAddress || isEditingAddress) ...[
            // Show form fields
            ShippingAddress(
              formKey: shippingFormKey,
              onNext: () {
                // This will be handled by the save button in header
              },
              hideButtons: true,
              disableScroll: true,
            ),
          ] else ...[
            // Show address summary and calculate shipping cost
            Builder(
              builder: (context) {
                if (isLoadingShippingCost) {
                  // Calculate shipping cost when address is displayed
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    _calculateShippingCostFromSavedAddress(address);
                  });
                }
                return _buildAddressDisplay(address);
              },
            ),
          ],
        ],
      ),
    );
  }

  // Calculate shipping cost from saved address
  Future<void> _calculateShippingCostFromSavedAddress(Address address) async {
    try {
      // Load cities and areas for the saved address
      final countries = await Services().widget.loadCountries();
      final country = countries?.firstWhereOrNull((element) =>
          element.id == address.country || element.code == address.country);

      if (country != null) {
        // Load cities for the country
        final citiesList =
            await Services().api.getCountryCities(country: country);

        // Find the saved city
        final savedCity = citiesList?.firstWhereOrNull(
          (element) =>
              element.id == address.stateId || element.code == address.stateId,
        );

        if (savedCity != null) {
          // Find the selected area within the city
          final areas = savedCity.areas
                  ?.map((e) => e.copyWith(city: savedCity))
                  .toList() ??
              [];

          final selectedArea = areas.firstWhereOrNull(
                (element) =>
                    element.name == address.city ||
                    element.nameAr == address.city,
              ) ??
              areas.firstOrNull;

          // Calculate shipping cost
          final isFreeShipping = (selectedArea?.freeShipping ?? false) ||
              (savedCity.freeShipping ?? false);

          setState(() {
            ShippingMethodModel.selectedShippingMethodCost.value =
                isFreeShipping
                    ? 0.0
                    : (selectedArea?.cost?.toDouble() ??
                        savedCity.cost?.toDouble() ??
                        0.0);

            isLoadingShippingCost = false;
          });

          printLog(
              'Calculated shipping cost from saved address: ${ShippingMethodModel.selectedShippingMethodCost.value}');
          printLog(
              'Free shipping: $isFreeShipping, Selected area: ${selectedArea?.name}, Saved city: ${savedCity.name}');
        }
      }
    } catch (e) {
      printLog('Error calculating shipping cost from saved address: $e');
    }
  }

  // Build address display content
  Widget _buildAddressDisplay(Address address) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (address.firstName?.isNotEmpty == true ||
              address.lastName?.isNotEmpty == true)
            Text(
              '${address.firstName ?? ''} ${address.lastName ?? ''}'.trim(),
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
          if (address.phoneNumber?.isNotEmpty == true) ...[
            const SizedBox(height: 4),
            Text(
              address.phoneNumber!,
              style: TextStyle(
                color: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.color
                    ?.withOpacity(0.7),
              ),
            ),
          ],
          // email
          if (address.email?.isNotEmpty == true) ...[
            const SizedBox(height: 4),
            Text(
              address.email!,
              style: TextStyle(
                color: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.color
                    ?.withOpacity(0.7),
              ),
            ),
          ],
          const SizedBox(height: 4),
          Text(
            [
              address.street,
              address.block,
              address.apartment,
              address.floor,
              address.city,
              selectedArea.value?.name,
              address.stateName,
            ].where((e) => e?.isNotEmpty == true).join(', '),
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  // Build fixed bottom checkout button for one-page checkout
  Widget _buildFixedCheckoutButton() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Consumer<CartModel>(
          builder: (context, cartModel, child) {
            return SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton.icon(
                onPressed: isLoading || isLoadingShippingCost
                    ? null
                    : () async {
                        // Validate all sections before proceeding
                        if (await _validateOnePageCheckout()) {
                          // Set loading state
                          setLoading(true);

                          // Use the same payment logic as the regular payment step
                          final paymentMethodModel =
                              Provider.of<PaymentMethodModel>(context,
                                  listen: false);
                          final cartModel =
                              Provider.of<CartModel>(context, listen: false);

                          // Call placeOrder with proper callbacks
                          placeOrder(paymentMethodModel, cartModel);
                        }
                      },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                icon: isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Icon(
                        CupertinoIcons.checkmark_seal,
                        size: 24,
                        color: Theme.of(context)
                            .primaryColor
                            .getColorBasedOnBackground,
                      ),
                label: Text(
                  isLoading
                      ? S.of(context).loading
                      : S.of(context).placeMyOrder.toUpperCase(),
                  style: Theme.of(context).textTheme.labelLarge!.copyWith(
                        color: Theme.of(context)
                            .primaryColor
                            .getColorBasedOnBackground,
                        fontWeight: FontWeight.bold,
                      ),
                  // const TextStyle(
                  //   fontSize: 14,
                  //   fontWeight: FontWeight.bold,
                  //   letterSpacing: 0.5,
                  // ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  // Validate all sections in one-page checkout
  Future<bool> _validateOnePageCheckout() async {
    // Validate address with comprehensive checks like in steps pages
    // if (cartModel.address == null || !cartModel.address!.isValid()) {
    //   _showValidationError(S.of(context).pleaseInputAddressFields);
    //   return false;
    // }

    if (isEditingAddress) {
      if (shippingFormKey.currentState?.validate() != true) {
        return false;
      }

      shippingFormKey.currentState!.save();
    }
    final cartModel = Provider.of<CartModel>(context, listen: false);

    final address = cartModel.address ?? userShippingAddress;
    // final address = userShippingAddress;
    // cartModel.address;

    // Check state/city validation like in steps pages
    if (address?.stateId == null || (address?.stateId?.isEmpty ?? false)) {
      _showValidationError(S.of(context).pleaseChooseValidCity);
      return false;
    }

    // Check map location if required by vendor
    final userPickedLocationFromMap =
        GetStorageService.hasData(key: LocalKeys.location) == true;

    // Note: currentVendor check would need to be added based on your vendor logic
    if (currentVendor?.config?.showMap == true && !userPickedLocationFromMap) {
      _showValidationError(S.of(context).pleasePickYourLocationFromMap);
      return false;
    }

    // Save address to local storage like in steps pages
    cartModel.setAddress(address);

    // Load shipping if needed (equivalent to _loadShipping in steps)
    // This ensures shipping methods are updated based on address
    // if (enabledShipping) {
    //   // Trigger shipping calculation
    //   cartModel.updateStateCheckoutButton();
    // }

    // Validate payment method
    final paymentMethodModel =
        Provider.of<PaymentMethodModel>(context, listen: false);
    if (paymentMethodModel.paymentMethods.isEmpty) {
      _showValidationError(S.of(context).chooseYourPaymentMethod);
      return false;
    }

    return true;
  }

  void _showValidationError(String message) {
    FlashHelper.errorMessage(
      context,
      message: message,
    );
    // ScaffoldMessenger.of(context).showSnackBar(
    //   SnackBar(
    //     content: Text(message),
    //     backgroundColor: Colors.red,
    //   ),
    // );
  }

  void _checkoutOnBack() {
    if (kPaymentConfig.enableReview) {
      goToReviewTab(true);
    } else {
      goToAddressTab(true);
    }
  }

  void _checkoutOnFinish(order) async {
    final cartModel = Provider.of<CartModel>(context, listen: false);

    setState(() {
      newOrder = order;
    });

    Analytics.triggerPurchased(order, context);

    await Services().widget.updateOrderAfterCheckout(context, order);
    cartModel.clearCart();

    // unawaited(context.read<WalletModel>().refreshWallet());

    if (isDesktop && order != null) {
      unawaited(Navigator.of(context)
          .pushNamed(RouteList.orderdSuccess, arguments: {'order': order}));
    }
  }

  /// tabIndex: 0
  void goToAddressTab([bool isGoingBack = false]) {
    if (kPaymentConfig.enableAddress) {
      setState(() {
        tabIndex = 0;
      });
    } else {
      // If address is disabled, go to review or payment
      if (!isGoingBack) {
        if (kPaymentConfig.enableReview) {
          goToReviewTab(isGoingBack);
        } else {
          goToPaymentTab(isGoingBack);
        }
      }
    }
  }

  /// tabIndex: 1
  void goToShippingTab([bool isGoingBack = false]) {
    if (enabledShipping) {
      setState(() {
        tabIndex = 1;
      });
    } else {
      if (isGoingBack) {
        goToAddressTab(isGoingBack);
      } else {
        goToReviewTab(isGoingBack);
      }
    }
  }

  /// tabIndex: 1 (review tab in new structure)
  void goToReviewTab([bool isGoingBack = false]) {
    if (kPaymentConfig.enableReview) {
      setState(() {
        tabIndex = 1;
      });
    } else {
      if (isGoingBack) {
        goToAddressTab(isGoingBack);
      } else {
        goToPaymentTab(isGoingBack);
      }
    }
  }

  /// tabIndex: 2 (payment tab in new structure)
  void goToPaymentTab([bool isGoingBack = false]) {
    if (!isGoingBack) {
      setState(() {
        tabIndex = kPaymentConfig.enableReview ? 2 : 1;
        isPayment = true;
      });
    }
  }

  void _onPressCloseButton() {
    if (Navigator.of(context).canPop()) {
      Navigator.popUntil(context,
          (Route<dynamic> route) => route.settings.name == RouteList.checkout);
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop(true);
      }
    } else {
      ExpandingBottomSheet.of(context, isNullOk: true)?.close();
    }
  }
}
