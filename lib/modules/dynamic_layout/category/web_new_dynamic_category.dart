import 'package:flutter/material.dart';

import '../../../common/config.dart';
import '../../../common/tools.dart';
import '../../../models/entities/category.dart';
import '../../../models/product_model.dart';
import '../../../widgets/common/flux_image.dart';
import '../config/index.dart';
import 'common_item_extension.dart';

class WebNewDynamicCategoryImageItem extends StatelessWidget {
  final Category cat;
  final products;
  final width;
  final height;
  final CommonItemConfig commonConfig;

  const WebNewDynamicCategoryImageItem({
    required this.cat,
    this.products,
    this.width,
    this.height,
    required this.commonConfig,
  });

  @override
  Widget build(BuildContext context) {
    // final height = height;
    // final height = MediaQuery.sizeOf(context).height * 0.3;

    double height = currentVendor?.isMarket == true ? 250 : 420;
    double width = currentVendor?.isMarket == true ? 250 : 300;

    double radius = currentVendor?.isMarket == true ? 500 : 30;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 20),
      child: GestureDetector(
        onTap: () {
          ProductModel.showList(
            cateName: cat.name,
            cateId: cat.id,
          );
        },
        child: Column(
          children: [
            SizedBox(
              height: height,
              width: width,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(radius),
                child: FluxImage(
                  imageUrl: cat.image!,
                  height: height,
                  width: width,
                  // width: MediaQuery.sizeOf(context).width,
                  fit: commonConfig.boxFit,
                ),
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            Text(cat.name ?? '',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                    )),
          ],
        ),
      ),
    );
  }
}
