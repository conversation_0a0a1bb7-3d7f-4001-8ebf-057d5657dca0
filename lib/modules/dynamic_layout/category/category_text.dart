import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:responsive_builder/responsive_builder.dart';

import '../../../models/category/category_model.dart';
import '../../../models/product_model.dart';
import '../../../widgets/common/background_color_widget.dart';
import '../config/category_config.dart';
import '../config/category_item_config.dart';
import 'category_text_item.dart';

const _defaultSeparateWidth = 24.0;

class CategoryTexts extends StatefulWidget {
  final CategoryConfig config;
  final int crossAxisCount;
  final Function onShowProductList;
  final Map<String?, String?> listCategoryName;

  const CategoryTexts({
    required this.config,
    required this.listCategoryName,
    required this.onShowProductList,
    this.crossAxisCount = 5,
    super.key,
  });

  @override
  State<CategoryTexts> createState() => _CategoryTextsState();
}

class _CategoryTextsState extends State<CategoryTexts> {

  @override
  void initState() {
    super.initState();
    final categoryModel = Provider.of<CategoryModel>(context, listen: false);
    if (categoryModel.categories == null || categoryModel.categories!.isEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        categoryModel.getCategories();
      });
    }
  }


  String _getCategoryName({required CategoryItemConfig item}) {
    if (widget.config.commonItemConfig.hideTitle) {
      return '';
    }

    String? name;

    /// not using the config Title from json
    if (!item.keepDefaultTitle && widget.listCategoryName.isNotEmpty) {
      name = widget.listCategoryName[item.category.toString()];
    } else {
      name = item.title;
    }
    return name ?? '';
  }

  @override
  Widget build(BuildContext context) {
    var numberItemOnScreen = widget.config.columns ?? widget.crossAxisCount;
    final categories = Provider.of<CategoryModel>(context).categories ?? [];

    numberItemOnScreen = getValueForScreenType(
      context: context,
      mobile: numberItemOnScreen,
      tablet: numberItemOnScreen + 3,
      desktop: numberItemOnScreen + 8,
    );

    var items = <Widget>[];

    for (var cat in categories) {
      // var name = _getCategoryName(item: item);

      items.add(
        CategoryTextItem(
          onTap: () => ProductModel.showList(
            cateName: cat.name,
            cateId: cat.id,
          ),
          // onShowProductList(item),
          name: cat.name,
          commonConfig: widget.config.commonItemConfig,
        ),
      );
    }

    if (widget.config.wrap == false && items.isNotEmpty) {
      return BackgroundColorWidget(
        enable: widget.config.enableBackground ?? false,
        width: MediaQuery.sizeOf(context).width,
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          padding: EdgeInsets.only(
            left: widget.config.marginLeft,
            right: widget.config.marginRight,
            top: widget.config.marginTop,
            bottom: widget.config.marginBottom,
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: items.expand((element) {
              return [
                element,
                ScreenTypeLayout.builder(
                  mobile: (BuildContext context) =>
                  const SizedBox(width: _defaultSeparateWidth),
                  tablet: (BuildContext context) =>
                  const SizedBox(width: _defaultSeparateWidth + 12),
                  desktop: (BuildContext context) =>
                  const SizedBox(width: _defaultSeparateWidth + 24),
                ),
              ];
            }).toList()
              ..removeLast(),
          ),
        ),
      );
    }

    return BackgroundColorWidget(
      enable: widget.config.enableBackground ?? true,
      width: MediaQuery.sizeOf(context).width,
      child: Container(
          margin: EdgeInsets.only(
            left: widget.config.marginLeft,
            right: widget.config.marginRight,
            top: widget.config.marginTop,
            bottom: widget.config.marginBottom,
          ),
          child: Wrap(
            spacing: widget.config.commonItemConfig.marginX,
            runSpacing: widget.config.commonItemConfig.marginY,
            children: List.generate(items.length, (i) => items[i]),
          )),
    );
  }
}