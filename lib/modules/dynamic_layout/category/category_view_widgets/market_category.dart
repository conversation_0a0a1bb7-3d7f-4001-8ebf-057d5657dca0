import 'package:flutter/material.dart';
import 'package:fstore/models/entities/category.dart';
import 'package:fstore/models/product_model.dart';
import 'package:fstore/modules/dynamic_layout/config/category_config.dart';

import '../../../../frameworks/strapi/services/strapi_service.dart';
import '../../../../widgets/common/flux_image.dart';

class MarketCategoryImageItem extends StatelessWidget {
  final Category cat;
  final products;
  final width;
  final height;
  final CommonItemConfig commonConfig;

  const MarketCategoryImageItem({
    required this.cat,
    this.products,
    this.width,
    this.height,
    required this.commonConfig,
  });

  @override
  Widget build(BuildContext context) {
    const radius = 15.0;

    return GestureDetector(
      onTap: () {
        selectedMainCategory.value = null;

        ProductModel.showList(
          cateName: cat.name,
          cateId: cat.id,
        );
      },
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(radius)),
            child: ClipRRect(
                borderRadius: BorderRadius.circular(radius),
                child: FluxImage(
                  imageUrl: cat.image ?? '',
                  width: 80,
                  height: 80,
                  fit: BoxFit.cover,
                )),
          ),
          const SizedBox(
            height: 10,
          ),
          SizedBox(
            width: 80,
            child: Text(
              cat.name ?? '',
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
              // const TextStyle(color: Colors.black, fontSize: 14),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
