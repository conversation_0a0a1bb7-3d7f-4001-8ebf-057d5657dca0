import 'package:flutter/material.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';
import 'package:fstore/models/entities/category.dart';
import 'package:fstore/models/product_model.dart';
import 'package:fstore/modules/dynamic_layout/config/category_config.dart';

import '../../../../common/extensions/buildcontext_ext.dart';
import '../../../../context_extensions.dart';
import '../../../../frameworks/strapi/services/strapi_service.dart';
import '../../../../widgets/common/flux_image.dart';
import '../../helper/helper.dart';

class RestaurantCategoryImageItem extends StatelessWidget {
  final Category cat;
  final products;
  final width;
  final height;
  final CommonItemConfig commonConfig;

  const RestaurantCategoryImageItem({
    required this.cat,
    this.products,
    this.width,
    this.height,
    required this.commonConfig,
  });

  @override
  Widget build(BuildContext context) {
    final height = Layout.isDisplayDesktop(context) ? 120.0 : 80.0;

    const radius = 0.0;

    return ClipRRect(
      borderRadius: BorderRadius.circular(radius),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 10.0),
        child: GestureDetector(
          onTap: () {
            selectedMainCategory.value = null;

            ProductModel.showList(
              cateName: cat.name,
              cateId: cat.id,
            );
          },
          child: Column(
            children: [
              // Container(
              //   alignment: Alignment.center,
              //   decoration: BoxDecoration(
              //     shape: BoxShape.circle,
              //     color: Theme.of(context).primaryColor.withOpacity(0.1),
              //   ),
              //   child: ClipRRect(
              //     borderRadius: BorderRadius.circular(100),
              //     clipBehavior: Clip.none,
              //     child: FluxImage(
              //       imageUrl: cat.image!,
              //       height: height,
              //       fit: BoxFit.cover,
              //     ),
              //   ),
              // ),

              CircleAvatar(
                radius: Layout.isDisplayDesktop(context)
                    ? 60
                    : context.isTablet
                        ? 50
                        : 40,
                backgroundColor:
                    Theme.of(context).primaryColor.withOpacity(0.1),
                child: Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(100),
                    child: FluxImage(
                      imageUrl: cat.image!,
                      height: height,
                      width: height,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 10),
              SizedBox(
                width: Layout.isDisplayDesktop(context)
                    ? 120
                    : context.screenWidth * 0.24,
                child: Text(
                  cat.name ?? '',
                  style: TextStyle(
                      color: context.isDark ? Colors.white : Colors.black,
                      fontSize: Layout.isDisplayDesktop(context) ? 18 : 15),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
