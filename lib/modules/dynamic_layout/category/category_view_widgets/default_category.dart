import 'package:flutter/material.dart';
import 'package:fstore/common/tools/image_resize.dart';
import 'package:fstore/models/entities/category.dart';
import 'package:fstore/models/product_model.dart';
import 'package:fstore/modules/dynamic_layout/config/category_config.dart';

import '../../../../frameworks/strapi/services/strapi_service.dart';
import '../../../../widgets/common/flux_image.dart';
import '../common_item_extension.dart';

class DefaultCategoryImageItem extends StatelessWidget {
  final Category cat;
  final products;
  final width;
  final height;
  final CommonItemConfig commonConfig;

  const DefaultCategoryImageItem({
    required this.cat,
    this.products,
    this.width,
    this.height,
    required this.commonConfig,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final itemWidth = width ?? screenSize.width / 3;

    final name = cat.name;
    final image = cat.image;

    final imageWidget = cat.image != null
        ? FluxImage(
            imageUrl: cat.image!,
            height: height,
            width: itemWidth,
            fit: BoxFit.cover,
            // commonConfig.boxFit,
          )
        : null;
    final border = commonConfig.enableBorder ? (commonConfig.border ?? 0.5) : 0;

    return Padding(
      padding: const EdgeInsets.only(left: 5.0, right: 10),
      child: GestureDetector(
        onTap: () {
          selectedMainCategory.value = null;

          ProductModel.showList(
            cateName: name,
            cateId: cat.id,
          );
        },
        child: Container(
          decoration: BoxDecoration(
              border: border > 0
                  ? Border.all(
                      color: Theme.of(context)
                          .colorScheme
                          .secondary
                          .withOpacity(0.5),
                      width: border.toDouble(),
                    )
                  : null,
              borderRadius: border > 0
                  ? const BorderRadius.all(Radius.circular(5.0))
                  : null,
              boxShadow: [
                if (commonConfig.boxShadow != null)
                  BoxShadow(
                    blurRadius: commonConfig.boxShadow!.blurRadius,
                    color: Theme.of(context)
                        .colorScheme
                        .secondary
                        .withOpacity(commonConfig.boxShadow!.colorOpacity),
                    offset: Offset(
                        commonConfig.boxShadow!.x, commonConfig.boxShadow!.y),
                  )
              ]),
          width: itemWidth,
          height: double.tryParse(height?.toString() ?? '200.0') ?? 200.0,
          padding: EdgeInsets.symmetric(
            horizontal: commonConfig.paddingX,
            vertical: commonConfig.paddingY,
          ),
          margin: EdgeInsets.symmetric(
            horizontal: commonConfig.marginX,
            vertical: commonConfig.marginY,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Expanded(
                child: Container(
                  width: itemWidth,
                  decoration: commonConfig.imageDecoration,
                  padding: EdgeInsets.all(commonConfig.imageSpacing),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(15),
                    // BorderRadius.circular(commonConfig.radius ?? 0.0),
                    child: imageWidget ??
                        ImageResize(
                          url: image,
                          fit: commonConfig.boxFit,
                          isResize: true,
                          size: kSize.small,
                        ),
                  ),
                ),
              ),
              Align(
                alignment: Alignment.center,
                // commonConfig.alignment,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 5),
                  child: Column(
                    crossAxisAlignment: [
                      Alignment.center,
                      Alignment.topCenter,
                      Alignment.bottomCenter
                    ].contains(commonConfig.alignment)
                        ? CrossAxisAlignment.center
                        : CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      // if (cat.showText ?? false) ...[
                      const SizedBox(height: 8),
                      Text(
                        cat.name ?? name!,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontSize: commonConfig.labelFontSize,
                                ),
                      ),
                      // ],
                      // if (cat.showDescription) ...[
                      //   const SizedBox(height: 4),
                      //   Text(
                      //     cat.description ??
                      //         S.of(context).totalProducts('$total'),
                      //     style: const TextStyle(
                      //       fontSize: 9,
                      //     ),
                      //   ),
                      // ]
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
