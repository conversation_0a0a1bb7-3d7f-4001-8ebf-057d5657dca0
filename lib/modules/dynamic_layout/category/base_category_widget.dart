// import 'package:flutter/material.dart';
// import 'package:fstore/modules/dynamic_layout/category/category_view_widgets/accessories_category.dart';
// import 'package:fstore/modules/dynamic_layout/category/category_view_widgets/clothes_category.dart';
// import 'package:fstore/modules/dynamic_layout/category/category_view_widgets/default_category.dart';
// import 'package:fstore/modules/dynamic_layout/category/category_view_widgets/market_category.dart';
//
// import '../../../common/config.dart';
// import '../../../models/entities/category.dart';
// import '../config/index.dart';
// import 'category_view_widgets/restaurant_category.dart';
//
// class BaseCategoryWidget extends StatelessWidget {
//   final Category cat;
//   final products;
//   final width;
//   final height;
//   final CommonItemConfig commonConfig;
//
//   const BaseCategoryWidget({
//     required this.cat,
//     this.products,
//     this.width,
//     this.height,
//     required this.commonConfig,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     if (currentVendor?.isRestaurant == true) {
//       return RestaurantCategoryImageItem(
//         cat: cat,
//         products: products,
//         width: width,
//         height: height,
//         commonConfig: commonConfig,
//       );
//     }
//
//     if (currentVendor?.isClothes == true) {
//       return ClothesCategoryImageItem(
//         cat: cat,
//         products: products,
//         width: width,
//         height: height,
//         commonConfig: commonConfig,
//       );
//     }
//
//     if (currentVendor?.isAccessories == true) {
//       return AccessoriesCategoryImageItem(
//         cat: cat,
//         products: products,
//         width: width,
//         height: height,
//         commonConfig: commonConfig,
//       );
//     }
//
//     if (currentVendor?.isMarket == true) {
//       return MarketCategoryImageItem(
//         cat: cat,
//         products: products,
//         width: width,
//         height: height,
//         commonConfig: commonConfig,
//       );
//     }
//
//     return DefaultCategoryImageItem(
//       cat: cat,
//       products: products,
//       width: width,
//       height: height,
//       commonConfig: commonConfig,
//     );
//   }
// }
