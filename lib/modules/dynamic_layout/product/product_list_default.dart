import 'dart:math';

import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:fstore/modules/dynamic_layout/helper/custom_physic.dart';
import 'package:provider/provider.dart';

import '../../../common/config.dart';
import '../../../common/tools.dart';
import '../../../models/index.dart' show AppModel, Product;
import '../../../services/index.dart';
import '../../../widgets/common/auto_silde_show.dart';
import '../../../widgets/common/background_color_widget.dart';
import '../../../widgets/common/flex_separated.dart';
import '../../../widgets/common/index.dart';
import '../../../widgets/common/parallax_image.dart';
import '../config/product_config.dart';
import '../helper/helper.dart';
import 'products_cards/restaurant/restaurant_featured_card.dart';

const _widthPercentForWebLayout = 0.32;

class ProductListDefault extends StatelessWidget {
  final maxWidth;
  final products;
  final int? row;
  final ProductConfig config;

  const ProductListDefault({
    super.key,
    this.maxWidth,
    this.products,
    this.row = 1,
    required this.config,
  });

  List<Widget> renderProduct(BuildContext context,
      {bool enableBackground = false}) {
    final isDesktop = Layout.isDisplayDesktop(context);
    var ratioProductImage =
        Provider.of<AppModel>(context, listen: false).ratioProductImage;

    /// allow override imageRatio when there is single Row
    if (config.rows == 1) {
      ratioProductImage = config.imageRatio;
    }

    var layout = config.layout ?? Layout.threeColumn;

    var width = isDesktop
        ? maxWidth * _widthPercentForWebLayout
        : Layout.buildProductWidth(
            screenWidth: maxWidth,
            layout: layout,
          );

    // TODO-New Arrival & Featured
    if (isDesktop) {
      final isThreeOrMoreProducts = products.length >= 3;

      return [
        for (var i = 0; i < min(3, products.length); i++)
          if (isThreeOrMoreProducts)
            Expanded(
              child: Services().widget.renderProductCardView(
                    item: products[i],
                    width: width,
                    maxWidth: maxWidth,
                    ratioProductImage: ratioProductImage,
                    config: config..showHeart = true,
                    useDesktopStyle: true,
                  ),
            )
          else
            Services().widget.renderProductCardView(
                  item: products[i],
                  width: width,
                  maxWidth: maxWidth,
                  ratioProductImage: ratioProductImage,
                  config: config..showHeart = true,
                  useDesktopStyle: false,
                )
      ];
    }

    final takeOnly5Products =
        products.length > 5 ? products.take(5).toList() : products;

    return [
      if (enableBackground)
        SizedBox(
          width:
              config.spaceWidth != null ? config.spaceWidth?.toDouble() : width,
        ),
      for (var i = 0; i < takeOnly5Products.length; i++)
        Services().widget.renderProductCardView(
              item: products[i],
              width: width,
              maxWidth: maxWidth,
              ratioProductImage: ratioProductImage,
              config: config,
            )
    ];
  }

  Widget renderHorizontal(BuildContext context,
      {bool enableBackground = false}) {
    final padding = enableBackground ||
            config.cardDesign.isSimpleCard ||
            config.cardDesign.isFlat
        ? 0.0
        : 12.0;
    var horizontalWidth = maxWidth - padding;
    var layout = config.layout ?? Layout.threeColumn;
    var scrollController = ScrollController();
    final listProducts = products is List ? (products as List) : [];

    /// wrap the product for Desktop mode
    if (Layout.isDisplayDesktop(context)) {
      return Padding(
        padding: const EdgeInsets.symmetric(
          vertical: 12.0,
          horizontal: 16,
        ),
        child: FlexSeparated.row(
          separationSize: 24,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: renderProduct(context, enableBackground: enableBackground),
        ),
      );
    }

    //TODO-Featured-Container
    final body = Container(
      color: Theme.of(context)
          .colorScheme
          .surface
          .withOpacity(enableBackground ? 0.0 : 1.0),
      padding: EdgeInsets.only(left: padding),
      constraints: BoxConstraints(
        minHeight: config.productListItemHeight,
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        controller: scrollController,
        padding: const EdgeInsets.symmetric(
          vertical: 8,
          horizontal: 8,
        ),
        physics: (config.isSnapping ?? false) && !kIsWeb
            ? CustomScrollPhysic(
                width: Layout.buildProductWidth(
                    screenWidth: horizontalWidth, layout: layout))
            : const ScrollPhysics(),
        child: FlexSeparated.row(
          separationSize: 16,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: renderProduct(context, enableBackground: enableBackground),
        ),
      ),
    );

    //TODO-Featured-Container-Web
    final webBody = Container(
      color: Theme.of(context)
          .colorScheme
          .surface
          .withOpacity(enableBackground ? 0.0 : 1.0),
      padding: EdgeInsets.only(left: padding),
      constraints: BoxConstraints(
        minHeight: config.productListItemHeight,
        maxHeight: currentVendor?.isRestaurant == true ? 240 : 400,
      ),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(
          horizontal: 8,
        ),
        separatorBuilder: (context, index) => const SizedBox(width: 16),
        itemCount: listProducts.length,
        itemBuilder: (context, index) {
          if (currentVendor?.isRestaurant == true) {
            return RestaurantFeaturedCard(product: listProducts[index]);
          }
          return Services().widget.renderProductCardView(
                item: listProducts[index],
                width: horizontalWidth,
                maxWidth: currentVendor?.isRestaurant == true ? 400 : 230,
                height: Layout.buildProductHeight(
                  layout: config.layout ?? Layout.threeColumn,
                  defaultHeight: maxWidth,
                ),
                ratioProductImage: Provider.of<AppModel>(context, listen: false)
                    .ratioProductImage,
                config: config,
              );
        },
      ),
    );

    if (currentVendor?.isRestaurant == true) {
      return RestaurantCarouselWidget(
        products: listProducts as List<Product>,
      );
    }

    return kIsWeb ? webBody : body;

    HandleAutoSlide.list(
      enable: config.enableAutoSliding,
      durationAutoSliding: config.durationAutoSliding ?? 15,
      numberOfItems: listProducts.length,
      controller: scrollController,
      child: body,
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = Layout.isDisplayDesktop(context);

    if (products == null) return const SizedBox();

    var haveCustomBackground =
        config.backgroundImage != null || config.backgroundColor != null;
    var backgroundHeight = config.backgroundHeight?.toDouble();
    var backgroundWidth = (config.backgroundWidthMode ?? false)
        ? maxWidth
        : config.backgroundWidth?.toDouble();

    var body =
        renderHorizontal(context, enableBackground: haveCustomBackground);

    var isDarkTheme = Provider.of<AppModel>(context, listen: false).darkTheme;

    if (haveCustomBackground) {
      body = Stack(
        children: [
          // TODO-FeaturedBackgroundContainer
          if (config.backgroundColor != null &&
              currentVendor?.isClothes == true)
            Container(
              height: isDesktop
                  ? (backgroundHeight ?? 150) * 2
                  : backgroundHeight ??
                      Layout.buildProductHeight(
                        layout: config.layout ?? Layout.threeColumn,
                        defaultHeight: maxWidth,
                      ),
              width: backgroundWidth,
              margin: config.marginBGP,
              decoration: BoxDecoration(
                color: isDarkTheme
                    ? const Color(0xff262934)
                    : config.backgroundColor,
                borderRadius: BorderRadius.circular(
                    isDesktop ? 15 : config.backgroundRadius),
              ),
            ),
          if (config.backgroundImage != null)
            Container(
              margin: config.marginBGP,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(config.backgroundRadius),
                child: config.enableParallax
                    ? ParallaxImage(
                        image: config.backgroundImage!,
                        fit: ImageTools.boxFit(config.backgroundBoxFit),
                        height: backgroundHeight,
                        ratio: config.parallaxImageRatio,
                        width: backgroundWidth,
                      )
                    : FluxImage(
                        imageUrl: config.backgroundImage!,
                        fit: ImageTools.boxFit(config.backgroundBoxFit),
                        height: backgroundHeight,
                        width: backgroundWidth,
                      ),
              ),
            ),
          Padding(
            padding: config.paddingBGP ?? const EdgeInsets.only(),
            child: body,
          ),
        ],
      );
    }

    return BackgroundColorWidget(
      enable: config.enableBackground,
      child: body,
    );
  }
}
