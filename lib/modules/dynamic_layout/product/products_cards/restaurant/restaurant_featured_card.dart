import 'package:carousel_slider/carousel_slider.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../../common/tools/price_tools.dart';
import '../../../../../models/app_model.dart';
import '../../../../../models/cart/cart_base.dart';
import '../../../../../models/category/category_model.dart';
import '../../../../../models/entities/product.dart';
import '../../../../../widgets/product/action_button_mixin.dart';

class RestaurantCarouselWidget extends StatelessWidget {
  final List<Product> products;

  const RestaurantCarouselWidget({super.key, required this.products});

  @override
  Widget build(BuildContext context) {
    return CarouselSlider(
      items: products
          .map((item) => ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: RestaurantFeaturedCard(product: item),
              ))
          .toList(),
      options: CarouselOptions(
        height: 230,
        aspectRatio: 16 / 9,
        viewportFraction: .8,
        initialPage: 0,
        enableInfiniteScroll: true,
        reverse: false,
        autoPlay: true,
        autoPlayInterval: const Duration(seconds: 5),
        autoPlayAnimationDuration: const Duration(milliseconds: 800),
        autoPlayCurve: Curves.fastOutSlowIn,
        enlargeCenterPage: true,
        enlargeFactor: 0.1,
        scrollDirection: Axis.horizontal,
      ),
    );
  }
}

class RestaurantFeaturedCard extends StatelessWidget with ActionButtonMixin {
  final Product product;

  const RestaurantFeaturedCard({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    final currencyRate =
        Provider.of<AppModel>(context, listen: false).currencyRate;
    final currency =
        Provider.of<CartModel>(context, listen: false).currencyCode;

    return GestureDetector(
      onTap: () => onTapProduct(context, product: product),
      child: Stack(
        children: <Widget>[
          Container(
            margin: const EdgeInsets.all(5.0),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Image.network(
                product.images.first,
                fit: BoxFit.cover,
                width: double.infinity,
              ),
            ),
          ),
          Container(
            margin: const EdgeInsets.all(5.0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.bottomCenter,
                end: Alignment.topCenter,
                colors: [
                  Colors.black.withOpacity(0.6),
                  Colors.black.withOpacity(0.1),
                  Colors.transparent,
                ],
              ),
            ),
          ),
          Container(
            margin: const EdgeInsets.all(15),
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: const BorderRadius.all(Radius.circular(50)),
            ),
            child: Text(
              Provider.of<CategoryModel>(context)
                      .categories
                      ?.firstWhereOrNull((element) =>
                          element.id == product.productCategory?.id?.toString())
                      ?.name ??
                  '',
              style: const TextStyle(
                fontSize: 15,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Positioned(
            bottom: 10,
            left: 10,
            right: 10,
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  num.tryParse(product.salePrice ?? '0') != null &&
                          num.tryParse(product.regularPrice!) != null &&
                          double.parse(product.salePrice!) <
                              double.parse(product.regularPrice!)
                      ? Row(
                          children: [
                            Text(
                              PriceTools.getCurrencyFormatted(
                                  product.salePrice, currencyRate,
                                  currency: currency)!,
                              style: const TextStyle(
                                fontSize: 16,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              PriceTools.getCurrencyFormatted(
                                  product.regularPrice, currencyRate,
                                  currency: currency)!,
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.white70,
                                decoration: TextDecoration.lineThrough,
                              ),
                            ),
                          ],
                        )
                      : Text(
                          PriceTools.getCurrencyFormatted(
                              product.price, currencyRate,
                              currency: currency)!,
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                          ),
                        ),
                  // Text(
                  //   PriceTools.getCurrencyFormatted(product.price, currencyRate,
                  //       currency: currency)!,
                  //   style: const TextStyle(
                  //     fontSize: 16,
                  //     color: Colors.white,
                  //   ),
                  // ),
                  Text(
                    product.name ?? '',
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                      fontSize: 20,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
