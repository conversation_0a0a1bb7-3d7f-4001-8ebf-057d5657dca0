import 'package:fstore/common/logger.dart';
import 'package:fstore/models/cart/cart_base.dart';
import 'package:fstore/models/user_model.dart';
import 'dart:async';
import 'dart:collection';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:paypal_payment/paypal_payment.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:universal_html/html.dart' as html;
//PlatformWebViewController
import 'package:webview_flutter/webview_flutter.dart';

import '../../../common/constants.dart';
import '../../../screens/checkout/widgets/convet_city_lang.dart';

class PaypalPaymentGatewayView extends StatefulWidget {
  final Function onSuccess;
  final  double totalPrice;


  const PaypalPaymentGatewayView({super.key, required this.onSuccess, required this.totalPrice});

  @override
  State<PaypalPaymentGatewayView> createState() => _PaypalPaymentGatewayViewState();
}

class _PaypalPaymentGatewayViewState extends State<PaypalPaymentGatewayView> {
  // web url
  final String url = 'https://idea2app.tech';
  final String clientId =
      'AT4EIFlis8p2GjMudzjH1ce3kXjkTDjkn0OUJb0dbkF5XHYuP8MJ5lSuQS4ooKMuhSrpD6sXoN19nOyJ';
  final String secretKey =
      'EMb51bp0yfVBUNUvObKB-wzvZOJx9hVhPpog74P5k8-wxNU9vO9_MGGbicQH1z7JvkMjjHI5zwWKbwkR';
  final String currencyCode = 'USD';

  @override
  void initState() {
    super.initState();
    // For paypal orders only in web we need to capture the payment after successfull callback to web route
    if (kIsWeb && Uri.base.queryParameters['PayerID'] != null) {
      checkForCallback();
    }
  }

  // only for web
  checkForCallback() async {
    await PaypalOrderService.captureOrder(
        "v2/checkout/orders/${Uri.base.queryParameters['PayerID']}/capture",
        clientId: clientId,
        sandboxMode: true,
        secretKey: secretKey);

    widget.onSuccess();
    // now call your backend endpoint to procced as you want!
    // print(response);
  }

  // Listen for callbacks for device not for web
  onSuccessCallback(value) {
    Log.w('PaypalSuccessCallback $value');
    widget.onSuccess();
  }

  onErrorCallback(error) {
    Log.e('PaypalErrorCallback $error');
    widget.onSuccess();
  }

  onCancelCallback() {
    Log.f('PaypalCancelCallback');
    widget.onSuccess();
  }

  @override
  Widget build(BuildContext context) {
    final amount = widget.totalPrice.toString();

    return PaypalOrderPayment(
      sandboxMode: false,
      returnURL: url,
      cancelURL: url,
      clientId: clientId,
      secretKey: secretKey,
      currencyCode: currencyCode,
      amount: amount,
      onSuccess: onSuccessCallback,
      onError: onErrorCallback,
      onCancel: onCancelCallback,
    );
    // return Scaffold(
    //   appBar: AppBar(title: const Text('Paypal Payment Example')),
    //   body: Center(
    //     child: Column(
    //       mainAxisAlignment: MainAxisAlignment.center,
    //       children: [
    //         ElevatedButton(
    //             onPressed: () {
    //               Navigator.of(context).push(MaterialPageRoute(
    //                   builder: (BuildContext context) => PaypalOrderPayment(
    //                       sandboxMode: true,
    //                       returnURL: url,
    //                       cancelURL: url,
    //                       clientId: clientId,
    //                       secretKey: secretKey,
    //                       currencyCode: currencyCode,
    //                       amount: amount,
    //                       onSuccess: onSuccessCallback,
    //                       onError: onErrorCallback,
    //                       onCancel: onCancelCallback)));
    //             },
    //             child: const Text('Paypal Payment (Order)')),
    //         const SizedBox(
    //           height: 25.0,
    //         ),
    //         ElevatedButton(
    //             onPressed: () {
    //               Navigator.of(context).push(MaterialPageRoute(
    //                   builder: (BuildContext context) =>
    //                       PaypalSubscriptionPayment(
    //                         sandboxMode: true,
    //                         clientId: clientId,
    //                         secretKey: secretKey,
    //                         productName: 'T-Shirt',
    //                         type: 'PHYSICAL',
    //                         planName: 'T-shirt plan',
    //                         billingCycles: [
    //                           {
    //                             'tenure_type': 'REGULAR',
    //                             'sequence': 1,
    //                             'total_cycles': 12,
    //                             'pricing_scheme': {
    //                               'fixed_price': {
    //                                 'currency_code': currencyCode,
    //                                 'value': amount
    //                               }
    //                             },
    //                             'frequency': const {
    //                               // "interval_unit": INTERVALUNIT.MONTH.name,
    //                               'interval_unit': 'MONTH',
    //                               'interval_count': 1
    //                             }
    //                           }
    //                         ],
    //                         paymentPreferences: const {
    //                           'auto_bill_outstanding': true
    //                         },
    //                         returnURL: url,
    //                         cancelURL: url,
    //                         onSuccess: onSuccessCallback,
    //                         onError: onErrorCallback,
    //                         onCancel: onCancelCallback,
    //                       )));
    //             },
    //             child: const Text('Paypal Payment (Subscription)'))
    //       ],
    //     ),
    //   ),
    // );
  }
}
