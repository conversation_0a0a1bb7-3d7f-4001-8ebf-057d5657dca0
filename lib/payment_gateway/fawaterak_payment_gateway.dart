import 'dart:async';
import 'dart:collection';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:universal_html/html.dart' as html;
//PlatformWebViewController
import 'package:webview_flutter/webview_flutter.dart';

import '../../../common/constants.dart';
import '../../../screens/checkout/widgets/convet_city_lang.dart';

Future<void> showFawaterakWebViewSheet(
  BuildContext context, {
  required String link,
  required int? invoiceId,
}) async {
  if (kIsWeb) {
    final isEng = isEnglish(context);

    html.window.open(link, 'webview');

    // Check the webhook function every 5 seconds
    Timer.periodic(const Duration(seconds: 5), (timer) async {
      var paymentSuccess = await checkIfInvoiceExistInDBWebHook(invoiceId);
      if (paymentSuccess) {
        timer.cancel();
        Navigator.of(context).pop(); // Close the dialog
        // Proceed with the order
        // Your order processing code here
      }
    });

    await QuickAlert.show(
      context: context,
      barrierDismissible: false,
      disableBackBtn: true,
      isSmall: true,
      type: QuickAlertType.loading,
      headerBackgroundColor: Theme.of(context).primaryColor,
      title: isEng ? 'Please wait' : 'برجاء الانتظار',
      text: isEng
          ? 'Please do not close or refresh this page until your payment is completed successfully..'
          : 'برجاء عدم إغلاق أو تحديث هذه الصفحة حتى يتم إتمام عملية الدفع بنجاح..',
    );
  } else {
    await showCupertinoModalPopup(
        context: context,
        builder: (context) {
          final controller = WebViewController()
            ..loadRequest(
              Uri.parse(
                link,
              ),
            )
            ..setJavaScriptMode(JavaScriptMode.unrestricted)
            ..setNavigationDelegate(
              NavigationDelegate(
                onPageFinished: (url) {
                  printLog('CurrentURL: $url');

                  if (url.contains('success')) {
                    Future.delayed(const Duration(seconds: 1), () {
                      Navigator.of(context).pop();
                    });
                  }
                },
              ),
            );

          return Padding(
            padding: const EdgeInsets.only(top: 20),
            child: WebViewWidget(
              controller: controller,
            ),
          );
        });
  }
}

// Future<bool> checkIfInvoiceExistInDBWebHook(int? invoiceId) async {
//   final response = await http.get(
//     Uri.parse(
//         // 'https://backend.idea2app.tech/api/webhooks?invoice_id=$invoiceId&sort[0]=createdAt:desc'),
//         'https://backend.idea2app.tech/api/webhooks?filters[invoice_id]=$invoiceId&sort[0]=createdAt:desc'),
//   );
//
//   final responseBody = jsonDecode(response.body)['data'];
//
//   printLog('WEBHOOK_RES $responseBody');
//
//   if (response.statusCode == 200) {
//     final responseBody = jsonDecode(response.body);
//
//     printLog('CheckIfInvoiceExistInDBWebHook $responseBody');
//
//     final responseData = responseBody as List?;
//
//     return responseBody != null &&
//         responseData!.isNotEmpty &&
//         responseData.firstOrNull['invoice_status'] == 'paid';
//   } else {
//     throw 'Failed to make online payment!';
//   }
// }

Future<bool> checkIfInvoiceExistInDBWebHook(int? invoiceId) async {
  final url =
      'https://backend.idea2app.tech/api/webhooks?filters[invoice_id]=$invoiceId&sort[0]=createdAt:desc';

  final response = await http.get(
    Uri.parse(url),
  );

  printLog('Web_Hooks_URL: $url');

  final responseBody = jsonDecode(response.body);

  printLog('WEBHOOK_RES $responseBody');

  if (response.statusCode == 200 || response.statusCode == 201) {
    final data = responseBody['data'];

    printLog('CheckIfInvoiceExistInDBWebHookData ${data}');

    final responseData = data as List?;

    return responseBody != null &&
        responseData!.isNotEmpty &&
        responseData.firstOrNull['invoice_status'] == 'paid';
  } else {
    throw 'Failed to make online payment!';
  }
}
