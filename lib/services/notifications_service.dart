import 'dart:convert';
import 'dart:developer';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:http/http.dart' as http;

import '../common/constants.dart';
import '../firebase_options.dart';

class NotificationService {
  static Future<void> _firebaseMessagingBackgroundHandler(
      RemoteMessage message) async {
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
    }
  }

  static void init() async {
    final fcm = FirebaseMessaging.instance;

    await fcm.requestPermission(
      alert: true,
      badge: true,
      provisional: false,
      sound: true,
    );

    await fcm.setForegroundNotificationPresentationOptions(
        badge: true, alert: true, sound: true);

    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    await fcm.subscribeToTopic('customers');

    return;
  }

  //? Get Token
  static Future<String> getToken() async {
    final fcm = FirebaseMessaging.instance;

    final token = await fcm.getToken();

    log('Fsakfnklfasfkn ${token ?? ''}');

    return token ?? '';
  }

  //? Subscribe to topic
  static Future<void> subscribeToTopic(String topic) async {
    final fcm = FirebaseMessaging.instance;

    log('SUBSCRIBED TO $topic');

    await fcm.subscribeToTopic(topic);
  }

  static Future<void> sendNotification(
      {required String title,
      required String body,
      required String userTokenOrTopic,
      bool isTopic = false}) async {
    const firebaseProjectId = 'idea2app-dev';

    const fcmUrl =
        'https://fcm.googleapis.com/v1/projects/$firebaseProjectId/messages:send';

    printLog('fcmUrl: $userTokenOrTopic');

    final accessToken = await AccessTokenFirebase().getAccessToken();

    printLog('accessToken: $accessToken');

    final message = <String, dynamic>{
      'message': {
        'token': isTopic ? '/topics/$userTokenOrTopic' : userTokenOrTopic,
        'notification': {
          'title': title,
          'body': body,
        },
      }
    };

    final response = await http.post(
      Uri.parse(fcmUrl),
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $accessToken',
      },
      body: jsonEncode(message),
    );

    if (response.statusCode == 200  || response.statusCode == 201) {
      printLog('NotificationSentSuccessfully ${response.body}');
    } else {
      printLog('Failed to send notification: ${response.statusCode}');
      printLog(response.body);
    }
  }

  //! Send Notification
  // static Future sendNotification({
  //   required String title,
  //   required String body,
  //   required String userToken,
  //   bool isTopic = false,
  // }) async {
  //   final data = jsonEncode(
  //     <String, dynamic>{
  //       'notification': <String, dynamic>{
  //         'title': title,
  //         'body': body,
  //       },
  //       'priority': 'high',
  //       'data': <String, dynamic>{
  //         'click_action': 'FLUTTER_NOTIFICATION_CLICK',
  //         'id': '1',
  //         'status': 'done'
  //       },
  //       'to': isTopic ? '/topics/$userToken' : userToken,
  //     },
  //   );
  //
  //   final response = await http.post(
  //     Uri.parse('https://fcm.googleapis.com/fcm/send'),
  //     headers: <String, String>{
  //       'Content-Type': 'application/json',
  //       'Authorization': 'key=${DefaultConfig.serverKey}',
  //     },
  //     body: data,
  //   );
  //
  //   log('notificationResponseData: ${response.body}');
  // }
}

class AccessTokenFirebase {
  static const firebaseMessagingScope =
      'https://www.googleapis.com/auth/firebase.messaging';

  Future<String> getAccessToken() async {
    final jsonMap = {
      'type': 'service_account',
      'project_id': 'idea2app-dev',
      'private_key_id': '61dad89510be63056a06feade9be12d94b8f8f5b',
      'private_key':
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      'client_email':
          '<EMAIL>',
      'client_id': '109942368878149529175',
      'auth_uri': 'https://accounts.google.com/o/oauth2/auth',
      'token_uri': 'https://oauth2.googleapis.com/token',
      'auth_provider_x509_cert_url':
          'https://www.googleapis.com/oauth2/v1/certs',
      'client_x509_cert_url':
          'https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-ad9jn%40idea2app-dev.iam.gserviceaccount.com',
      'universe_domain': 'googleapis.com'
    };

    final client = await clientViaServiceAccount(
        ServiceAccountCredentials.fromJson(jsonMap), [firebaseMessagingScope]);

    final accessToken = client.credentials.accessToken.data;

    return accessToken;
  }
}
