import 'package:collection/collection.dart';

import '../../../common/constants.dart';
import '../../../data/boxes.dart';
import '../../../services/index.dart';
import '../../vendor/extra_setting_model.dart';
import '../../index.dart';
import '../cart_item_meta_data.dart';
import 'cart_mixin.dart';

/// Everything relate to Local storage
mixin LocalMixin on CartMixin {
  // Helper function to compare extras lists
  bool _areExtrasEqual(
      List<ExtraSettingsModel> extras1, List<ExtraSettingsModel> extras2) {
    if (extras1.length != extras2.length) return false;

    for (var extra1 in extras1) {
      if (!extras2.any((extra2) => extra1.id == extra2.id)) {
        return false;
      }
    }
    return true;
  }

  void saveCartToLocal(String key,
      {Product? product,
      int? quantity = 1,
      String? size,
      String? color,
      List<ExtraSettingsModel>? extras,
      CartItemMetaData? cartItemMetaData}) {
    try {
      var items = UserBox().productsInCart;

      product?.selectedColor = color;
      product?.selectedSize = size;
      product?.selectedExtras = extras ?? [];

      final sizePrice = product?.sizes
          .firstWhereOrNull((element) => element.name == product.selectedSize)
          ?.price;
      printLog('safasfsafs222323f ${sizePrice}');

      // Calculate extras total price
      // final extrasPrice =
      //     extras?.fold<num>(0, (sum, extra) => sum + (extra.price ?? 0)) ?? 0;
      final totalPrice =
          (sizePrice ?? double.tryParse(product?.price ?? '0') ?? 0);
      // +
      // extrasPrice;

      product?.price = totalPrice.toString();
      product?.regularPrice = totalPrice.toString();
      product?.salePrice = totalPrice.toString();

      printLog('ffffggdgdgdgdgdfsfsf ${product?.toJson()}');

      final newItem = {
        'product': product?.toJson(),
        'quantity': quantity,
        'size': size,
        'color': color,
        'extras': extras?.map((e) => e.toJson()).toList() ?? [],
        'price': totalPrice,
        'key': key,
        'cartItemMetaData': cartItemMetaData?.toJson(),
      };

      printLog('QQQUANTITY: ${items?.length}');

      if (items != null && items.isNotEmpty) {
        final existed =
            items.firstWhereOrNull((item) => item['key'] == key) != null;
        if (existed) {
          items =
              items.map((item) => item['key'] == key ? newItem : item).toList();
        } else {
          items.add(newItem);
        }
      } else {
        items = [newItem];
      }

      var selectedSizeAndColorList =
          UserBox().selectedSizeAndColorAndExtras[product!.id.toString()] ?? [];

      var indexToUpdate = selectedSizeAndColorList.indexWhere((element) =>
          element.size == size &&
          element.color == color &&
          _areExtrasEqual(element.extras, extras ?? []));

      if (indexToUpdate != -1) {
        printLog(
            'selectedSizeAndColorList[indexToUpdate]Before: ${selectedSizeAndColorList[indexToUpdate].quantity}');

        selectedSizeAndColorList[indexToUpdate].quantity = quantity;

        printLog(
            'selectedSizeAndColorList[indexToUpdate].After: ${selectedSizeAndColorList[indexToUpdate].quantity}\nUpdated quantity: $quantity');
      } else {
        selectedSizeAndColorList.add(SelectedSizeAndColor(
          size: size,
          color: color,
          quantity: quantity,
          price: totalPrice,
          extras: extras ?? [],
        ));

        printLog('selectedSizeAndColorListAdd: $quantity');
      }

      UserBox().selectedSizeAndColorAndExtras = {
        ...UserBox().selectedSizeAndColorAndExtras,
        product.id.toString(): selectedSizeAndColorList,
      };

      UserBox().productsInCart = items;

      printLog('QQQUANTITY2222: ${items.length}');
    } catch (err) {
      printLog('[saveCartToLocal] failed: $err');
    }
  }

  Future<void> updateQuantityCartLocal(
      {String? key, int quantity = 1, String? size, String? color}) async {
    try {
      printLog('Update Quantity Cart_Local $key - $quantity');
      var items = UserBox().productsInCart;
      if (items != null && items.isNotEmpty) {
        for (var item in items) {
          final productItem = Product.fromLocalJson(item['product']);
          final ids = productItem.id.toString();
          final colorItem = item['color'];
          final sizeItem = item['size'];

          if (productItem.id == ids && color == colorItem && size == sizeItem) {
            item['quantity'] = quantity;
            break;
          }
        }
      }

      UserBox().productsInCart = items;
    } catch (err) {
      printLog(err);
    }
  }

  Future<void> clearCartLocal() async {
    UserBox().productsInCart = null;
  }

  void removeProductLocal(String key) {
    try {
      final items = UserBox().productsInCart;
      if (items != null && items.isNotEmpty) {
        final ids = key.split('-');
        var newItems = <Map>[];
        for (var item in items) {
          if (Product.fromLocalJson(item['product']).id != ids[0]) {
            newItems.add(item);
          }
        }
        UserBox().productsInCart = newItems;
      }
    } catch (err) {
      printLog(err);
    }
  }

  void getCartInLocal() {
    if (ServerConfig().isVendorManagerType()) {
      return;
    }
    try {
      final items = UserBox().productsInCart;

      if (items != null && items.isNotEmpty) {
        for (final item in items) {
          addProductToCart(
            product: Product.fromLocalJson(item['product']),
            quantity: item['quantity'],
            cartItemMetaData: item['cartItemMetaData'] != null
                ? CartItemMetaData.fromLocalJson(item['cartItemMetaData'])
                : null,
            isSaveLocal: false,
            notify: () {},
          );
        }
      }
    } catch (err, trace) {
      printError(err, trace, '::::::::: Get Cart In Local Error');
    }
  }

  // Adds a product to the cart.
  String addProductToCart({
    required Product product,
    int? quantity = 1,
    CartItemMetaData? cartItemMetaData,
    required Function notify,
    isSaveLocal = true,
    List<ExtraSettingsModel>? extras,
  }) {
    var message = '';

    var key = product.id.toString();
    var total = !productsInCart.containsKey(key)
        ? quantity
        : (productsInCart[key]! + quantity!);
    var stockQuantity = product.stockQuantity;

    if (!product.manageStock) {
      productsInCart[key] = total;
    } else if (total! <= stockQuantity!) {
      if (product.minQuantity == null && product.maxQuantity == null) {
        productsInCart[key] = total;
      } else if (product.minQuantity != null && product.maxQuantity == null) {
        total < product.minQuantity!
            ? message = 'Minimum quantity is ${product.minQuantity}'
            : productsInCart[key] = total;
      } else if (product.minQuantity == null && product.maxQuantity != null) {
        total > product.maxQuantity!
            ? message =
                'You can only purchase ${product.maxQuantity} for this product'
            : productsInCart[key] = total;
      } else if (product.minQuantity != null && product.maxQuantity != null) {
        if (total >= product.minQuantity! && total <= product.maxQuantity!) {
          productsInCart[key] = total;
        } else {
          if (total < product.minQuantity!) {
            message = 'Minimum quantity is ${product.minQuantity}';
          }
          if (total > product.maxQuantity!) {
            message =
                'You can only purchase ${product.maxQuantity} for this product';
          }
        }
      }
    } else {
      message = 'Currently we only have $stockQuantity of this product';
    }

    if (message.isEmpty) {
      item[product.id] = product;

      if (isSaveLocal) {
        saveCartToLocal(
          key,
          product: product,
          quantity: quantity,
          extras: extras,
        );
      }
    }

    notify();
    return message;
  }
}
