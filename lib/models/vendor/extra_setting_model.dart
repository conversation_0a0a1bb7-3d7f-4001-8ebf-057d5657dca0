import '../../frameworks/strapi/services/strapi_service.dart';

class ExtraSettingsModel {
  final int? id;
  final String name;
  final String nameEn;
  final String nameAr;
  final int? stock;
  final num? price;

  bool get isOutOfStock => stock != null && stock! <= 0;

  const ExtraSettingsModel({
    this.id,
    this.name = '',
    this.nameEn = '',
    this.nameAr = '',
    this.stock,
    this.price,
  });

  factory ExtraSettingsModel.fromJson(Map<dynamic, dynamic> json) {
    final name = translatedText(textEn: json['name'], textAr: json['name_ar']);

    return ExtraSettingsModel(
      id: json['id'],
      name: name,
      nameEn: json['name'] ?? '',
      nameAr: json['name_ar'] ?? '',
      stock: int.tryParse(json['stock']?.toString() ?? ''),
      price: num.tryParse(json['price']?.toString() ?? ''),
    );
  }

  factory ExtraSettingsModel.fromProductJson(Map<String, dynamic> json) {
    final name = translatedText(textEn: json['name'], textAr: json['name_ar']);

    return ExtraSettingsModel(
      name: name,
      nameEn: json['name_en'] ?? json['name'] ?? '',
      nameAr: json['name_ar'] ?? '',
      stock: int.tryParse(json['stock'] ?? ''),
      price: json['price'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'name_en': nameEn,
      'name_ar': nameAr,
      if (stock != null) 'stock': stock,
      if (price != null) 'price': price,
    };
  }

  //! Copy With
  ExtraSettingsModel copyWith({
    int? id,
    String? name,
    String? nameEn,
    String? nameAr,
    num? price,
    int? stock,
  }) {
    return ExtraSettingsModel(
      id: id ?? this.id,
      name: name ?? this.name,
      nameEn: nameEn ?? this.nameEn,
      nameAr: nameAr ?? this.nameAr,
      stock: stock ?? this.stock,
      price: price ?? this.price,
    );
  }

  @override
  String toString() {
    return toJson().toString();
  }
}

class SelectedSizeAndColor {
  final String? size;
  final String? color;
  final num? price;
  int? quantity;
  final List<ExtraSettingsModel> extras;

  SelectedSizeAndColor({
    this.size,
    this.color,
    this.quantity,
    this.price,
    this.extras = const [],
  });

  factory SelectedSizeAndColor.fromJson(Map<String, dynamic> json) {
    return SelectedSizeAndColor(
      size: json['size'] ?? '',
      color: json['color'] ?? '',
      quantity: json['quantity'] ?? 1,
      price: json['price'],
      extras: json['extras'] == null
          ? []
          : (json['extras'] as List)
              .map((e) => ExtraSettingsModel.fromJson(e))
              .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'size': size,
      'color': color,
      'quantity': quantity,
      'price': price,
      'extras': extras.map((e) => e.toJson()).toList(),
    };
  }

  //! Copy With
  SelectedSizeAndColor copyWith({
    String? size,
    String? color,
    int? quantity,
    num? price,
    List<ExtraSettingsModel>? extras,
  }) {
    return SelectedSizeAndColor(
      size: size ?? this.size,
      color: color ?? this.color,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
      extras: extras ?? this.extras,
    );
  }

  @override
  String toString() {
    return toJson().toString();
  }
}
