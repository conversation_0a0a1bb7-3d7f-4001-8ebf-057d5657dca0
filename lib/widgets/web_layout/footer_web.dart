import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';

import '../../common/config.dart';
import '../../common/tools/tools.dart';
import '../../generated/l10n.dart';
import '../../models/app_model.dart';
import '../../screens/settings/layouts/mixins/setting_action_mixin.dart';
import 'web_layout_mixin.dart';
import 'widgets/download_app_widget.dart';
import 'widgets/my_account_widget.dart';

class FooterWeb extends StatefulWidget {
  const FooterWeb({super.key});

  @override
  State<FooterWeb> createState() => _FooterWebState();
}

class _FooterWebState extends State<FooterWeb>
    with WebLayoutMixin, SettingActionMixin {
  void _onTapOpenUrlWeb(String urlWeb) => onTapOpenUrl(context, urlWeb);

  @override
  Widget build(BuildContext context) {
    final appModel = Provider.of<AppModel>(context, listen: false);

    final themeConfig = appModel.themeConfig;

    return Container(
      height: 280,
      color: Theme.of(context).primaryColor.withOpacity(0.1),
      margin: const EdgeInsets.only(top: 10),
      padding: const EdgeInsets.symmetric(horizontal: 64.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: LayoutBuilder(
              builder: (_, constraints) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              // FollowSocialWidget(
                              //   padding: EdgeInsets.zero,
                              //   title: const SizedBox(),
                              //   sizeIcon: 30,
                              //   onTap: _onTapOpenUrlWeb,
                              //   color: Theme.of(context)
                              //       .scaffoldBackgroundColor
                              //       .getColorBasedOnBackground,
                              // ),
                              if (canShowVendorLogo())
                                Padding(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 20.0),
                                    child: CircleAvatar(
                                      backgroundColor: Colors.transparent,
                                      radius: 70,
                                      backgroundImage: NetworkImage(
                                          currentVendor?.logoUrl ??
                                              appModel.themeConfig.logo),
                                    )),

                              const Spacer(),

                              // if (currentVendor == null || isIdea2App)
                              Column(
                                children: [
                                  const SocialWidget(),
                                  DownloadAppWidget(
                                    onTap: _onTapOpenUrlWeb,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        // RichText(
                        //   text: TextSpan(
                        //     text: 'Create your free website now, with ',
                        //     style: const TextStyle(
                        //         color: Colors.black, fontSize: 20),
                        //     children: [
                        //       TextSpan(
                        //         text: 'Idea2App',
                        //         style: const TextStyle(
                        //           color: Colors.blue,
                        //           decoration: TextDecoration.underline,
                        //         ),
                        //         recognizer: TapGestureRecognizer()
                        //           ..onTap = () =>
                        //               _onTapOpenUrlWeb('https://idea2app.tech'),
                        //       ),
                        //     ],
                        //   ),
                        // ),
                      ],
                    ),
                    const MyAccountWidget(),
                  ],
                );
              },
            ),
          ),
          const Divider(),
          Row(
            children: [
              if (idea2AppCopyRightUrl.isNotEmpty) ...[
                Padding(
                  padding: const EdgeInsetsDirectional.only(start: 11)
                      .copyWith(bottom: 10),
                  child: InkWell(
                      onTap: () => onTapOpenUrl(context, idea2AppCopyRightUrl),
                      child: Text(S.of(context).copyright)),
                ),
                const Spacer(),
              ],
              if (currentVendor?.aboutVendor?.privacy != null &&
                  (currentVendor?.aboutVendor?.privacy.isNotEmpty ?? false))
                Padding(
                  padding: const EdgeInsets.only(bottom: 10),
                  child: InkWell(
                    hoverColor: Colors.transparent,
                    onTap: () => QuickAlert.show(
                      title: S.of(context).privacyPolicy,
                      text: currentVendor?.aboutVendor?.privacy,
                      context: context,
                      type: QuickAlertType.info,
                      confirmBtnText: S.of(context).close,
                    ),
                    child: Text(
                      S.of(context).privacyTerms,
                      style: const TextStyle(),
                    ),
                  ),
                ),
              if (currentVendor?.aboutVendor?.about != null &&
                  (currentVendor?.aboutVendor?.about.isNotEmpty ?? false))
                Padding(
                  padding:
                      const EdgeInsets.only(bottom: 10, right: 10, left: 10),
                  child: InkWell(
                    hoverColor: Colors.transparent,
                    onTap: () => QuickAlert.show(
                      title: S.of(context).aboutUs,
                      text: currentVendor?.aboutVendor?.about,
                      context: context,
                      type: QuickAlertType.info,
                      confirmBtnText: S.of(context).close,
                    ),
                    child: Text(
                      S.of(context).aboutUs,
                      style: const TextStyle(),
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }
}

class SocialWidget extends StatelessWidget {
  const SocialWidget({super.key});

  @override
  Widget build(BuildContext context) {
    const radiusSize = 25.0;

    return Column(
      children: [
        // TODO-Settings
        // if (currentVendor?.aboutVendor?.privacy != null &&
        //     (currentVendor?.aboutVendor?.privacy.isNotEmpty ?? false))
        //   Padding(
        //     padding: const EdgeInsets.symmetric(horizontal: 16),
        //     child: SettingItemWidget(
        //       cardStyle: SettingItemStyle.listTile,
        //       icon: Icons.privacy_tip_outlined,
        //       title: S.of(context).privacyPolicy,
        //       trailing: const Icon(
        //         Icons.arrow_forward_ios,
        //         size: 18,
        //         color: kGrey600,
        //       ),
        //       onTap: () {
        //         QuickAlert.show(
        //           title: S.of(context).privacyPolicy,
        //           text: currentVendor?.aboutVendor?.privacy,
        //           context: context,
        //           type: QuickAlertType.info,
        //           confirmBtnText: S.of(context).close,
        //         );
        //       },
        //     ),
        //   ),
        //
        // // about
        // if (currentVendor?.aboutVendor?.about != null &&
        //     (currentVendor?.aboutVendor?.about.isNotEmpty ?? false))
        //   Padding(
        //     padding: const EdgeInsets.symmetric(horizontal: 16),
        //     child: SettingItemWidget(
        //       cardStyle: SettingItemStyle.listTile,
        //       icon: Icons.info_outline,
        //       title: S.of(context).aboutUs,
        //       trailing: const Icon(
        //         Icons.arrow_forward_ios,
        //         size: 18,
        //         color: kGrey600,
        //       ),
        //       onTap: () {
        //         QuickAlert.show(
        //           title: S.of(context).aboutUs,
        //           text: currentVendor?.aboutVendor?.about,
        //           context: context,
        //           type: QuickAlertType.info,
        //           confirmBtnText: S.of(context).close,
        //         );
        //       },
        //     ),
        //   ),

        Row(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 42),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  if (currentVendor?.aboutVendor?.whatsapp != null &&
                      (currentVendor?.aboutVendor?.whatsapp.isNotEmpty ??
                          false))
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 6),
                      child: CircleAvatar(
                        radius: radiusSize,
                        backgroundColor: const Color(0xFF25D366),
                        child: IconButton(
                          onPressed: () {
                            Tools.launchURL(
                              'https://wa.me/${currentVendor?.aboutVendor?.whatsapp}',
                            );
                          },
                          icon: const FaIcon(
                            FontAwesomeIcons.whatsapp,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  if (currentVendor?.aboutVendor?.facebook != null &&
                      (currentVendor?.aboutVendor?.facebook.isNotEmpty ??
                          false))
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 6),
                      child: CircleAvatar(
                        radius: radiusSize,
                        backgroundColor: Colors.blue,
                        child: IconButton(
                          onPressed: () {
                            Tools.launchURL(
                              currentVendor?.aboutVendor?.facebook,
                            );
                          },
                          icon: const FaIcon(
                            FontAwesomeIcons.facebook,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  if (currentVendor?.aboutVendor?.instagram != null &&
                      (currentVendor?.aboutVendor?.instagram.isNotEmpty ??
                          false))
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 6),
                      child: CircleAvatar(
                        radius: radiusSize,
                        backgroundColor: Colors.pink,
                        child: IconButton(
                          onPressed: () {
                            Tools.launchURL(
                              currentVendor?.aboutVendor?.instagram,
                            );
                          },
                          icon: const FaIcon(
                            FontAwesomeIcons.instagram,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  if (currentVendor?.aboutVendor?.tiktok != null &&
                      (currentVendor?.aboutVendor?.tiktok.isNotEmpty ?? false))
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 6),
                      child: CircleAvatar(
                        radius: radiusSize,
                        backgroundColor: Colors.black,
                        child: IconButton(
                          onPressed: () {
                            Tools.launchURL(
                              currentVendor?.aboutVendor?.tiktok,
                            );
                          },
                          icon: const FaIcon(
                            FontAwesomeIcons.tiktok,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
