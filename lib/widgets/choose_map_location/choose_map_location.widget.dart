import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:location/location.dart';

import '../../common/config.dart';
import '../../common/constants/local_keys.dart';
import '../../common/tools/flash.dart';
import '../../context_extensions.dart';
import '../../models/entities/country_state.dart';
import '../../screens/checkout/widgets/convet_city_lang.dart' show isEnglish;
import '../../services/get_storage_service.dart';
import 'mixins/map.mixin.dart';

class ChooseMapLocationWidget extends HookWidget with MapMixin {
  final LatLng? initLocation;
  final ValueNotifier<LatLng?> selectedLocation;
  final CountryState? selectedCity;

  const ChooseMapLocationWidget({
    super.key,
    this.initLocation,
    this.selectedCity,
    required this.selectedLocation,
  });

  @override
  Widget build(BuildContext context) {
    final markers = useState<Set<Marker>>({});
    final polygons = useState<Set<Polygon>>({});
    final mainLocation = useState<LatLng>(initLocation ??
        selectedCity?.cityLocation ??
        Configurations.defaultLocation);

    final myMapController = useState<GoogleMapController?>(null);
    final isLocationValid = useState<bool>(true);

    final checkBoundaries = (currentVendor?.config?.showBoundaries ?? false) &&
        (selectedCity?.cityBoundaries.isNotEmpty ?? false);

    final cityBoundaries = selectedCity?.cityBoundaries ?? <LatLng>[];

    Future<void> initData() async {
      mainLocation.value =
          selectedCity?.cityLocation ?? Configurations.defaultLocation;

      // Add boundary polygon
      polygons.value = {
        Polygon(
          polygonId: const PolygonId('cityBoundary'),
          points: cityBoundaries,
          strokeWidth: 2,
          strokeColor: Colors.red,
          fillColor: Colors.red.withOpacity(0.1),
        ),
      };

      // Get current location
      final location = Location();
      final currentLocation = await location.getLocation();
      final currentLatLng =
          LatLng(currentLocation.latitude!, currentLocation.longitude!);

      // Navigate to current location
      await myMapController.value?.animateCamera(
        CameraUpdate.newLatLng(currentLatLng),
      );
    }

    bool isInsideBoundary(LatLng point) {
      bool inside = false;
      for (var i = 0, j = cityBoundaries.length - 1;
          i < cityBoundaries.length;
          j = i++) {
        if (((cityBoundaries[i].latitude > point.latitude) !=
                (cityBoundaries[j].latitude > point.latitude)) &&
            (point.longitude <
                (cityBoundaries[j].longitude - cityBoundaries[i].longitude) *
                        (point.latitude - cityBoundaries[i].latitude) /
                        (cityBoundaries[j].latitude -
                            cityBoundaries[i].latitude) +
                    cityBoundaries[i].longitude)) {
          inside = !inside;
        }
      }
      return inside;
    }

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        initData();
      });

      return () {};
    }, []);

    return Stack(
      children: [
        SafeArea(
          child: GoogleMap(
            initialCameraPosition: CameraPosition(
              target: mainLocation.value,
              zoom: 15.0,
            ),
            myLocationEnabled: true,
            myLocationButtonEnabled: false,
            markers: markers.value,
            zoomControlsEnabled: false,
            polygons: checkBoundaries ? polygons.value : {},
            mapType: MapType.normal,
            onMapCreated: (controller) {
              myMapController.value = controller;
            },
            onCameraMove: (position) {
              if (checkBoundaries) {
                final valid = isInsideBoundary(position.target);
                isLocationValid.value = valid;
                selectedLocation.value = valid ? position.target : null;
              } else {
                selectedLocation.value = position.target;
              }
            },
          ),
        ),
        const Center(
          child: Icon(
            Icons.location_on_rounded,
            color: Colors.red,
            size: 50,
          ),
        ),
        Positioned(
          bottom: 40,
          left: 40,
          right: 40,
          child: ElevatedButton(
            onPressed: isLocationValid.value || !checkBoundaries
                ? () {
                    GetStorageService.setData(
                      key: LocalKeys.location,
                      value: selectedLocation.value?.toJson(),
                    );

                    Navigator.of(context).pop();

                    FlashHelper.message(
                      context,
                      message: context.tr.locationSelectedSuccessfully,
                    );
                  }
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: isLocationValid.value
                  ? Theme.of(context).primaryColor
                  : Colors.grey,
            ),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.done_all),
                  const SizedBox(width: 10),
                  Text(context.tr.selectLocation,
                      style: Theme.of(context)
                          .textTheme
                          .labelLarge!
                          .copyWith(color: Colors.white)),
                ],
              ),
            ),
          ),
        ),
        if (!isLocationValid.value && checkBoundaries)
          Positioned(
            top: 100,
            left: 20,
            right: 20,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                context.tr.locationOutsideBoundary,
                style: const TextStyle(color: Colors.white),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        Positioned(
          top: 32,
          right: isEnglish(context) ? 20 : null,
          left: isEnglish(context) ? null : 20,
          child: CircleAvatar(
            radius: 22,
            backgroundColor: Colors.white,
            child: IconButton(
              onPressed: () async {
                final location = Location();
                final currentLocation = await location.getLocation();
                final currentLatLng = LatLng(
                    currentLocation.latitude!, currentLocation.longitude!);

                await myMapController.value?.animateCamera(
                  CameraUpdate.newLatLng(currentLatLng),
                );
              },
              icon: const Icon(
                Icons.my_location,
                color: Colors.black,
                size: 23,
              ),
            ),
          ),
        ),
        Positioned(
          top: 30,
          left: isEnglish(context) ? 20 : null,
          right: isEnglish(context) ? null : 20,
          child: CircleAvatar(
            backgroundColor: Colors.white,
            child: IconButton(
              icon: const Icon(CupertinoIcons.back, color: Colors.black),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ),
        ),
      ],
    );
  }
}
