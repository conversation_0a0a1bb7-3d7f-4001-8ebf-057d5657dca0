import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

mixin MapMixin {
  animateCameraPosition(
    ValueNotifier<GoogleMapController?> myMapController, {
    required double lat,
    required double lng,
  }) {
    myMapController.value?.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: LatLng(lat, lng),
          zoom: 15.0,
        ),
      ),
    );
  }

  baseMarker(String icon) => BitmapDescriptor.fromAssetImage(
      const ImageConfiguration(
        size: Size(150, 512),
        devicePixelRatio: 52,
      ),
      icon);

  // setUserMarkers(
  //   BuildContext context, {
  //   required ValueNotifier<Set<Marker>> markers,
  //   required List<UserModel> users,
  // }) async {
  //   markers.value.clear();
  //   final icon = await baseMarker('assets/images/user-marker.png');
  //   // final icon = await baseMarker('assets/images/profile.png');
  //
  //   for (var user in users) {
  //     markers.value.add(
  //       Marker(
  //           markerId: MarkerId(user.id.toString()),
  //           position: LatLng(user.lat, user.long),
  //           icon: icon,
  //           onTap: () => showModalBottomSheet(
  //                 context: context,
  //                 builder: (context) {
  //                   return ProfileScreen(
  //                     userId: user.id,
  //                   );
  //                 },
  //               ),
  //           infoWindow: InfoWindow(
  //             title: user.name,
  //             snippet: user.bio,
  //           )),
  //     );
  //   }
  // }
}
