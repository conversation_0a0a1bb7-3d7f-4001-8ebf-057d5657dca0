import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../screens/checkout/widgets/convet_city_lang.dart';

class AnimatedTextWidget extends HookWidget {
  final int currentIndex;
  final String text;
  final TextStyle? style;

  const AnimatedTextWidget({
    super.key,
    required this.currentIndex,
    required this.text,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    // Animation Controller
    final animationController = useAnimationController(
      duration: const Duration(milliseconds: 500),
    )..forward();

    // Define the animations
    final opacityAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: animationController, curve: Curves.easeIn),
    );

    final alignAnimation = AlignmentTween(
      begin:
      isEnglish(context) ?
      Alignment.centerRight: Alignment.centerLeft,
      end: isEnglish(context) ? Alignment.centerLeft: Alignment.centerRight,
    ).animate(
      CurvedAnimation(parent: animationController, curve: Curves.easeOutQuad),
    );

    final scaleAnimation = Tween<double>(begin: 0.8, end: 1).animate(
      CurvedAnimation(parent: animationController, curve: Curves.easeOutBack),
    );

    // Re-trigger the animation on currentIndex change
    useEffect(() {
      animationController.reset();
      animationController.forward();
      return null;
    }, [currentIndex]);

    return FadeTransition(
      opacity: opacityAnimation,
      child: AlignTransition(
        alignment: alignAnimation,
        child: ScaleTransition(
          scale: scaleAnimation,
          child: Text(
            text,
            style: style,
          ),
        ),
      ),
    );
  }
}
