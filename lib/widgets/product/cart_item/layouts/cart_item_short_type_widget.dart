import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../common/tools.dart';
import '../../../../data/boxes.dart';
import '../../../../generated/l10n.dart';
import '../../../../models/app_model.dart';
import '../cart_item_state_ui.dart';

const _kMinHeightCard = 60.0;

class CartItemShortTypeWidget extends StatelessWidget {
  const CartItemShortTypeWidget(
    this.stateUI, {
    super.key,
    required this.constraintsCurrent,
  });

  final CartItemStateUI stateUI;
  final BoxConstraints constraintsCurrent;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    var currency = Provider.of<AppModel>(context).currency;
    final currencyRate = Provider.of<AppModel>(context).currencyRate;

    final selectedProducts =
        UserBox().selectedSizeAndColorAndExtras[stateUI.product.id.toString()];

    final priceSizeIsNull = selectedProducts?.lastOrNull?.price == null;

    final price = priceSizeIsNull
        ? stateUI.price
        : selectedProducts?.map((e) {
            final price = e.price ?? 0;
            final quantity = e.quantity ?? 1;
            return '${e.size}: ${PriceTools.getCurrencyFormatted(price * quantity, currencyRate, currency: currency) ?? ''}';
          }).join(', ');

    return GestureDetector(
      onTap: () => stateUI.onTapProduct(context, product: stateUI.product),
      child: SizedBox(
        height: _kMinHeightCard,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            SizedBox(
              width: _kMinHeightCard,
              height: _kMinHeightCard,
              child: ImageResize(
                url: stateUI.imageFeature,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(width: 16.0),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    stateUI.product.name ?? '',
                    style: TextStyle(
                      color: theme.colorScheme.secondary,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    '${S.of(context).quantity}: ${stateUI.quantity}',
                    style: TextStyle(
                      color: theme.colorScheme.secondary,
                      fontSize: 11,
                    ),
                  ),
                  if (stateUI.showPrice(context))
                    Text(
                      price ?? '',
                      // stateUI.price!,
                      style: TextStyle(
                        color: theme.colorScheme.secondary,
                        fontSize: 11,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
