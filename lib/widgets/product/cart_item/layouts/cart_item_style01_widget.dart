import 'dart:developer';

import 'package:flutter/material.dart';

import '../../../../common/config.dart';
import '../../../../common/config/models/cart_config.dart';
import '../../../../common/theme/colors.dart';
import '../../../../common/tools.dart';
import '../../../../data/boxes.dart';
import '../../../../generated/l10n.dart';
import '../../../../models/vendor/extra_setting_model.dart';
import '../../../../screens/products/colors/choose_color_card.dart';
import '../../../../screens/products/sizes/choose_size_card.dart';
import '../../../../screens/products/extras/choose_extra_card.dart';
import '../../../../services/index.dart';
import '../../quantity_selection/quantity_selection.dart';
import '../cart_item_state_ui.dart';

const _kMinHeightCard = 110.0;

class CartItemStyle01Widget extends StatelessWidget {
  const CartItemStyle01Widget(this.stateUI,
      {super.key,
      required this.constraintsCurrent,
      required this.setState,
      this.fromCheckout = false});

  final CartItemStateUI stateUI;
  final BoxConstraints constraintsCurrent;
  final Function setState;
  final bool fromCheckout;

  // Helper function to compare extras lists
  bool _areExtrasEqual(
      List<ExtraSettingsModel> extras1, List<ExtraSettingsModel> extras2) {
    if (extras1.length != extras2.length) return false;

    for (var extra1 in extras1) {
      if (!extras2.any((extra2) => extra1.id == extra2.id)) {
        return false;
      }
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final selectedProducts =
        UserBox().selectedSizeAndColorAndExtras[stateUI.product.id.toString()];

    final infosWidget = [
      if (stateUI.product.options != null &&
          stateUI.cartItemMetaData?.options != null)
        Services().widget.renderOptionsCartItem(
            stateUI.product, stateUI.cartItemMetaData?.options),
      if (stateUI.cartItemMetaData?.variation != null)
        Services().widget.renderVariantCartItem(
              context,
              stateUI.cartItemMetaData!.variation!,
              stateUI.cartItemMetaData?.options,
              style: AttributeProductCartStyle.short,
            ),
      if (stateUI.cartItemMetaData?.addonsOptions?.isNotEmpty ?? false)
        Services().widget.renderAddonsOptionsCartItem(
            context, stateUI.cartItemMetaData?.addonsOptions),
      if (selectedProducts != null && selectedProducts.isNotEmpty)
        ListView.separated(
          shrinkWrap: true,
          itemCount: selectedProducts.length,
          physics: const NeverScrollableScrollPhysics(),
          separatorBuilder: (context, index) => const SizedBox(height: 8),
          itemBuilder: (context, index) {
            final selectedProduct = selectedProducts[index];

            return Row(
              children: [
                // if (kProductDetail.showStockQuantity)
                if (stateUI.showQuantity)
                  QuantitySelection(
                    enabled: (stateUI.inStock || stateUI.isOnBackorder) &&
                        stateUI.onChangeQuantity != null,
                    width: 60,
                    height: 32,
                    color: Theme.of(context).colorScheme.secondary,
                    limitSelectQuantity: stateUI.isOnBackorder
                        ? kCartDetail['maxAllowQuantity']
                        : stateUI.limitQuantity,
                    value: selectedProduct.quantity,
                    onChanged: fromCheckout
                        ? null
                        : (newQuantity) {
                            // Get the current selectedSizeAndColor map
                            var currentMap =
                                UserBox().selectedSizeAndColorAndExtras;

                            // Find the corresponding SelectedSizeAndColor list
                            var selectedSizeAndColorList =
                                currentMap[stateUI.product.id.toString()];

                            if (selectedSizeAndColorList != null) {
                              // Create a new list that replaces the item with updated quantity
                              var newList =
                                  selectedSizeAndColorList.map((item) {
                                if (item.size == selectedProduct.size &&
                                    item.color == selectedProduct.color &&
                                    _areExtrasEqual(
                                        item.extras, selectedProduct.extras)) {
                                  // Create a new SelectedSizeAndColor object with updated quantity
                                  return SelectedSizeAndColor(
                                    size: item.size,
                                    color: item.color,
                                    quantity: newQuantity,
                                    price: item.price,
                                    extras: item.extras,
                                  );
                                } else {
                                  return item;
                                }
                              }).toList();

                              // Update the map with the new list
                              currentMap[stateUI.product.id.toString()] =
                                  newList;

                              // Update the map in the UserBox
                              UserBox().selectedSizeAndColorAndExtras =
                                  currentMap;

                              setState(() {});
                            }

                            // get old quantity for other colors
                            var totalOtherProductQuantities = 0;

                            for (var item in selectedSizeAndColorList!) {
                              totalOtherProductQuantities += item.quantity ?? 1;
                            }

                            // subtract the old quantity of the selected color
                            totalOtherProductQuantities -=
                                selectedProduct.quantity ?? 1;

                            // add the new quantity of the selected color
                            final allQuantities =
                                totalOtherProductQuantities + newQuantity;

                            log('OLD_QUANTITY: $totalOtherProductQuantities + $newQuantity = $allQuantities');

                            return stateUI.onChangeQuantity
                                    ?.call(allQuantities) ??
                                true;
                          },
                    // (newQuantity) {
                    //     // Get the current selectedSizeAndColor map
                    //     var currentMap = UserBox().selectedSizeAndColor;
                    //
                    //     // Find the corresponding SelectedSizeAndColor list
                    //     var selectedSizeAndColorList =
                    //         currentMap[stateUI.product.id.toString()];
                    //
                    //     if (selectedSizeAndColorList != null) {
                    //       // Create a new list that replaces the item with updated quantity
                    //       var newList =
                    //           selectedSizeAndColorList.map((item) {
                    //         if (item.size == selectedProduct.size &&
                    //             item.color == selectedProduct.color) {
                    //           // Create a new SelectedSizeAndColor object with updated quantity
                    //           return SelectedSizeAndColor(
                    //             size: item.size,
                    //             color: item.color,
                    //             quantity: newQuantity,
                    //             price: item.price,
                    //           );
                    //         } else {
                    //           return item;
                    //         }
                    //       }).toList();
                    //
                    //       // Update the map with the new list
                    //       currentMap[stateUI.product.id.toString()] =
                    //           newList;
                    //
                    //       // Update the map in the UserBox
                    //       UserBox().selectedSizeAndColor = currentMap;
                    //
                    //       setState(() {});
                    //     }
                    //
                    //     // get old quantity for other colors
                    //     var totalOtherProductQuantities = 0;
                    //
                    //     for (var item in selectedSizeAndColorList!) {
                    //       totalOtherProductQuantities += item.quantity ?? 1;
                    //     }
                    //
                    //     // subtract the old quantity of the selected color
                    //     totalOtherProductQuantities -=
                    //         selectedProduct.quantity ?? 1;
                    //
                    //     // add the new quantity of the selected color
                    //     final allQuantities =
                    //         totalOtherProductQuantities + newQuantity;
                    //
                    //     log('OLD_QUANTITY: $totalOtherProductQuantities + $newQuantity = $allQuantities');
                    //
                    //     return stateUI.onChangeQuantity
                    //             ?.call(allQuantities) ??
                    //         true;
                    //   },
                    // useNewDesign: false,
                  ),
                const SizedBox(
                  width: 10,
                ),
                Align(
                  alignment: Alignment.centerRight,
                  child: ChooseSizeCard(
                    size: selectedProduct.size ?? '',
                    isSelected: true,
                    fromCart: true,
                  ),
                ),
                const SizedBox(
                  width: 10,
                ),
                Align(
                  alignment: Alignment.centerRight,
                  child: ChooseColorCard(
                    color: selectedProduct.color ?? '',
                  ),
                ),

                // Display extras
                if (selectedProduct.extras.isNotEmpty) ...[
                  const SizedBox(width: 10),
                  Wrap(
                    spacing: 5,
                    children: selectedProduct.extras.map((extra) {
                      return ChooseExtraCard(
                        extra: extra,
                        fromCart: true,
                      );
                    }).toList(),
                  ),
                ],
              ],
            );
          },
        ),
      if (stateUI.product.store != null &&
          (stateUI.product.store?.name != null &&
              stateUI.product.store!.name!.trim().isNotEmpty))
        const SizedBox(height: 10),
      if (!stateUI.inStock || stateUI.isOnBackorder) const SizedBox(height: 5),
      if (stateUI.isOnBackorder)
        Text(S.of(context).backOrder,
            style: TextStyle(
              color: kStockColor.backorder,
            )),
      if (!stateUI.isOnBackorder && !stateUI.inStock)
        Text(
          S.of(context).outOfStock,
          style: const TextStyle(color: Colors.red),
        ),
      if (!stateUI.isOnBackorder &&
          stateUI.inStock &&
          stateUI.quantity != null &&
          stateUI.quantity! > stateUI.limitQuantity)
        Text(
          S.of(context).quantityProductExceedInStock,
          style: const TextStyle(color: Colors.red),
        ),
      Row(
        children: [
          if (stateUI.showStoreName &&
              (stateUI.product.store?.name?.isNotEmpty ?? false))
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(top: 5.0),
                child: Text(
                  stateUI.product.store?.name ?? '',
                  style: TextStyle(
                      color: theme.colorScheme.secondary, fontSize: 12),
                ),
              ),
            )
          else
            const Spacer(),
          // if (stateUI.showQuantity)
          //   QuantitySelection(
          //     enabled: (stateUI.inStock || stateUI.isOnBackorder) &&
          //         stateUI.onChangeQuantity != null,
          //     width: 25,
          //     height: 25,
          //     quantityStep: stateUI.product.quantityStep,
          //     enabledTextBox: stateUI.enabledTextBoxQuantity,
          //     color: Theme.of(context).colorScheme.secondary,
          //     limitSelectQuantity: stateUI.isOnBackorder
          //         ? kCartDetail['maxAllowQuantity']
          //         : stateUI.limitQuantity,
          //     value: stateUI.quantity,
          //     onChanged: (value) {
          //       return stateUI.onChangeQuantity
          //               ?.call(value == -1 ? 1 : value) ??
          //           true;
          //     },
          //     style: QuantitySelectionStyle.style02,
          //   ),
        ],
      ),
      if (stateUI.isPWGiftCardProduct)
        Services().widget.renderPWGiftCardInfoCartItem(
              stateUI.cartItemMetaData?.pwGiftCardInfo,
              quantity: stateUI.quantity,
              price: stateUI.cartItemMetaData?.variation?.price,
            ),
    ];

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (stateUI.enableTopDivider == true)
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 5.0),
            child: Divider(color: kGrey200, height: 1),
          ),
        Row(
          key: ValueKey(stateUI.product.id),
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () =>
                    stateUI.onTapProduct(context, product: stateUI.product),
                child: Container(
                  margin: const EdgeInsets.only(left: 16, right: 16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  constraints: const BoxConstraints(
                    minHeight: _kMinHeightCard,
                  ),
                  child: Column(
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: <Widget>[
                          const SizedBox(width: 10),
                          SizedBox(
                            width: 80,
                            height: 80,
                            child: ImageResize(
                              url: stateUI.imageFeature,
                              fit: BoxFit.cover,
                            ),
                          ),
                          const SizedBox(width: 16.0),
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsetsDirectional.symmetric(
                                      vertical: 5)
                                  .copyWith(end: 10),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Expanded(
                                        child: Text(
                                          stateUI.product.name ?? '',
                                          style: TextStyle(
                                            color: theme.colorScheme.secondary,
                                            fontWeight: FontWeight.w500,
                                          ),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      if (stateUI.onRemove != null)
                                        GestureDetector(
                                          onTap: stateUI.onRemove,
                                          behavior: HitTestBehavior.translucent,
                                          child: Container(
                                            height: 35,
                                            padding: const EdgeInsets.symmetric(
                                              vertical: 5,
                                              horizontal: 0,
                                            ).copyWith(left: 10),
                                            child: const Align(
                                              alignment: Alignment.topCenter,
                                              child: Icon(
                                                Icons.close_rounded,
                                                size: 18,
                                              ),
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  const SizedBox(height: 5),
                                  if (!Services().widget.hideProductPrice(
                                      context, stateUI.product))
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          stateUI.price!,
                                          style: TextStyle(
                                            color: theme.colorScheme.secondary,
                                            fontSize: 13,
                                          ),
                                        ),
                                        // Show quantity discount indicator
                                        if (_hasQuantityDiscount())
                                          Container(
                                            margin:
                                                const EdgeInsets.only(top: 4),
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 6, vertical: 2),
                                            decoration: BoxDecoration(
                                              color:
                                                  Colors.green.withOpacity(0.1),
                                              borderRadius:
                                                  BorderRadius.circular(4),
                                              border: Border.all(
                                                color: Colors.green
                                                    .withOpacity(0.3),
                                                width: 1,
                                              ),
                                            ),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Icon(
                                                  Icons.local_offer,
                                                  color: Colors.green,
                                                  size: 12,
                                                ),
                                                const SizedBox(width: 4),
                                                Text(
                                                  _getDiscountText(context),
                                                  style: TextStyle(
                                                    color: Colors.green,
                                                    fontSize: 10,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                      ],
                                    ),
                                  if (infosWidget.isNotEmpty)
                                    const SizedBox(height: 10),
                                  ...infosWidget,
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                      if (stateUI.cartItemMetaData?.selectedComponents
                              ?.isNotEmpty ??
                          false)
                        Services().widget.renderSelectedComponentsCartItem(
                            context,
                            stateUI.cartItemMetaData?.selectedComponents),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
      ],
    );
  }

  bool _hasQuantityDiscount() {
    final selectedProducts =
        UserBox().selectedSizeAndColorAndExtras[stateUI.product.id.toString()];

    if (selectedProducts == null || selectedProducts.isEmpty) {
      return false;
    }

    final totalQuantity = selectedProducts.fold<int>(
      0,
      (previousValue, element) => previousValue + (element.quantity ?? 0),
    );

    return stateUI.product.minQuantitySaleNumber != null &&
        stateUI.product.quantitySale != null &&
        stateUI.product.quantitySale! > 0 &&
        totalQuantity >= (stateUI.product.minQuantitySaleNumber ?? 0);
  }

  String _getDiscountText(BuildContext context) {
    if (stateUI.product.isQuantitySalePercentage == true) {
      return '${stateUI.product.quantitySale}% ${S.of(context).off}';
    } else {
      return S.of(context).discountApplied;
    }
  }
}
