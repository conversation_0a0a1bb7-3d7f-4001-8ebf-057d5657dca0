import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../common/config.dart';
import '../../../../common/theme/colors.dart';
import '../../../../common/tools.dart';
import '../../../../data/boxes.dart';
import '../../../../generated/l10n.dart';
import '../../../../models/app_model.dart';
import '../../../../models/cart/cart_base.dart';
import '../../../../models/vendor/extra_setting_model.dart';
import '../../../../screens/products/colors/choose_color_card.dart';
import '../../../../screens/products/sizes/choose_size_card.dart';
import '../../../../screens/products/extras/choose_extra_card.dart';
import '../../../../services/index.dart';
import '../../quantity_selection/quantity_selection.dart';
import '../cart_item_state_ui.dart';

class CartItemNormalWidget extends StatelessWidget {
  const CartItemNormalWidget(
    this.stateUI, {
    super.key,
    this.fromCheckout = false,
    this.widthImageFeature = 100,
    this.heightImageFeature = 100,
    required this.setState,
  });

  final CartItemStateUI stateUI;

  final double widthImageFeature;
  final double heightImageFeature;
  final bool fromCheckout;

  final Function setState;

  // Helper function to compare extras lists
  bool _areExtrasEqual(
      List<ExtraSettingsModel> extras1, List<ExtraSettingsModel> extras2) {
    if (extras1.length != extras2.length) return false;

    for (var extra1 in extras1) {
      if (!extras2.any((extra2) => extra1.id == extra2.id)) {
        return false;
      }
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final selectedProductSizes =
        UserBox().selectedSizeAndColorAndExtras[stateUI.product.id.toString()];
    final currencyRate = context.read<AppModel>().currencyRate;
    final currency = context.read<CartModel>().currencyCode;

    final haveSelectedProductSize = selectedProductSizes != null &&
        selectedProductSizes.isNotEmpty &&
        selectedProductSizes.firstOrNull?.size != null &&
        selectedProductSizes.firstOrNull!.size!.isNotEmpty;

    final productSizesPrice =
        selectedProductSizes?.fold(0.0, (previousValue, element) {
      final basePrice = (element.price ?? 0) * (element.quantity ?? 1);
      final extrasPrice = element.extras
              .fold<num>(0, (sum, extra) => sum + (extra.price ?? 0)) *
          (element.quantity ?? 1);
      return previousValue + basePrice + extrasPrice;
    });

    //TODO-CartSizeMobile
    final productPrice = haveSelectedProductSize &&
            productSizesPrice != null &&
            productSizesPrice > 0
        ? PriceTools.getCurrencyFormatted(productSizesPrice, currencyRate,
            currency: currency)
        : stateUI.price;

    // final copiedProduct = stateUI.product.copyWith(
    //   sizes: stateUI.product.sizes,
    // )

    log('afsfasfsa ${stateUI.product.toJson()}');

    return Column(
      children: [
        if (stateUI.enableTopDivider == true)
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 10.0),
            child: Divider(color: kGrey200, height: 1),
          ),
        Row(
          key: ValueKey(stateUI.product.id),
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (stateUI.onRemove != null)
              IconButton(
                icon: const Icon(Icons.remove_circle_outline),
                onPressed: stateUI.onRemove,
              ),
            Expanded(
              child: GestureDetector(
                onTap: () =>
                    stateUI.onTapProduct(context, product: stateUI.product),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    SizedBox(
                      width: widthImageFeature, //constraints.maxWidth * 0.25,
                      height: heightImageFeature, //constraints.maxWidth * 0.3,
                      child: ClipRRect(
                          borderRadius: BorderRadius.circular(7.0),
                          child: ImageResize(
                            url: stateUI.imageFeature,
                            fit: BoxFit.cover,
                          )),
                    ),
                    const SizedBox(width: 16.0),
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              stateUI.product.name ?? '',
                              style: TextStyle(
                                color: theme.colorScheme.secondary,
                              ),
                              maxLines: 4,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 7),
                            if (stateUI.showPrice(context))
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    productPrice ?? '',
                                    // stateUI.price!,
                                    style: TextStyle(
                                        color: theme.colorScheme.secondary,
                                        fontSize: 14),
                                  ),
                                  // Show quantity discount indicator
                                  if (_hasQuantityDiscount())
                                    Container(
                                      margin: const EdgeInsets.only(top: 4),
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 6, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: Colors.green.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(4),
                                        border: Border.all(
                                          color: Colors.green.withOpacity(0.3),
                                          width: 1,
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          const Icon(
                                            Icons.local_offer,
                                            color: Colors.green,
                                            size: 12,
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            _getDiscountText(context),
                                            style: const TextStyle(
                                              color: Colors.green,
                                              fontSize: 10,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                ],
                              ),
                            const SizedBox(height: 10),
                            if (stateUI.product.options != null &&
                                stateUI.cartItemMetaData?.options != null)
                              Services().widget.renderOptionsCartItem(
                                  stateUI.product,
                                  stateUI.cartItemMetaData?.options),
                            if (stateUI.cartItemMetaData?.variation != null)
                              Services().widget.renderVariantCartItem(
                                  context,
                                  stateUI.cartItemMetaData!.variation!,
                                  stateUI.cartItemMetaData?.options),
                            if (stateUI.cartItemMetaData?.addonsOptions
                                    ?.isNotEmpty ??
                                false)
                              Services().widget.renderAddonsOptionsCartItem(
                                  context,
                                  stateUI.cartItemMetaData?.addonsOptions),
                            if (selectedProductSizes != null &&
                                selectedProductSizes.isNotEmpty)
                              ListView.separated(
                                shrinkWrap: true,
                                itemCount: selectedProductSizes.length,
                                physics: const NeverScrollableScrollPhysics(),
                                separatorBuilder: (context, index) =>
                                    const SizedBox(height: 8),
                                itemBuilder: (context, index) {
                                  final selectedProduct =
                                      selectedProductSizes[index];
                                  log('asfasaf ${selectedProduct.price}');

                                  final isOnlyOneSize =
                                      selectedProductSizes.length == 1;

                                  return Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          // if (kProductDetail.showStockQuantity)
                                          if (stateUI.showQuantity)
                                            QuantitySelection(
                                              enabled: (stateUI.inStock ||
                                                      stateUI.isOnBackorder) &&
                                                  stateUI.onChangeQuantity !=
                                                      null,
                                              width: 60,
                                              height: 32,
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .secondary,
                                              limitSelectQuantity:
                                                  stateUI.isOnBackorder
                                                      ? kCartDetail[
                                                          'maxAllowQuantity']
                                                      : stateUI.limitQuantity,
                                              value: selectedProduct.quantity,
                                              onChanged: fromCheckout
                                                  ? null
                                                  : (newQuantity) {
                                                      // Get the current selectedSizeAndColor map
                                                      var currentMap = UserBox()
                                                          .selectedSizeAndColorAndExtras;

                                                      // Find the corresponding SelectedSizeAndColor list
                                                      var selectedSizeAndColorList =
                                                          currentMap[stateUI
                                                              .product.id
                                                              .toString()];

                                                      if (selectedSizeAndColorList !=
                                                          null) {
                                                        // Create a new list that replaces the item with updated quantity
                                                        var newList =
                                                            selectedSizeAndColorList
                                                                .map((item) {
                                                          if (item.size ==
                                                                  selectedProduct
                                                                      .size &&
                                                              item.color ==
                                                                  selectedProduct
                                                                      .color &&
                                                              _areExtrasEqual(
                                                                  item.extras,
                                                                  selectedProduct
                                                                      .extras)) {
                                                            // Create a new SelectedSizeAndColor object with updated quantity
                                                            return SelectedSizeAndColor(
                                                              size: item.size,
                                                              color: item.color,
                                                              quantity:
                                                                  newQuantity,
                                                              price: item.price,
                                                              extras:
                                                                  item.extras,
                                                            );
                                                          } else {
                                                            return item;
                                                          }
                                                        }).toList();

                                                        // Update the map with the new list
                                                        currentMap[stateUI
                                                                .product.id
                                                                .toString()] =
                                                            newList;

                                                        // Update the map in the UserBox
                                                        UserBox()
                                                                .selectedSizeAndColorAndExtras =
                                                            currentMap;

                                                        setState(() {});
                                                      }

                                                      // get old quantity for other colors
                                                      var totalOtherProductQuantities =
                                                          0;

                                                      for (var item
                                                          in selectedSizeAndColorList!) {
                                                        totalOtherProductQuantities +=
                                                            item.quantity ?? 1;
                                                      }

                                                      // subtract the old quantity of the selected color
                                                      totalOtherProductQuantities -=
                                                          selectedProduct
                                                                  .quantity ??
                                                              1;

                                                      // add the new quantity of the selected color
                                                      final allQuantities =
                                                          totalOtherProductQuantities +
                                                              newQuantity;

                                                      log('OLD_QUANTITY: $totalOtherProductQuantities + $newQuantity = $allQuantities');

                                                      return stateUI
                                                              .onChangeQuantity
                                                              ?.call(
                                                                  allQuantities) ??
                                                          true;
                                                    },
                                            ),

                                          if (selectedProduct
                                                  .color?.isNotEmpty ??
                                              false) ...[
                                            const SizedBox(
                                              width: 10,
                                            ),
                                            Align(
                                              alignment: Alignment.centerRight,
                                              child: ChooseColorCard(
                                                color:
                                                    selectedProduct.color ?? '',
                                              ),
                                            ),
                                          ],

                                          if (selectedProduct
                                                  .size?.isNotEmpty ??
                                              false) ...[
                                            const SizedBox(
                                              width: 10,
                                            ),
                                            Align(
                                              alignment: Alignment.centerRight,
                                              child: ChooseSizeCard(
                                                size:
                                                    selectedProduct.size ?? '',
                                                isSelected: true,
                                                fromCart: true,
                                              ),
                                            ),
                                          ],

                                          if (!isOnlyOneSize) ...[
                                            const SizedBox(
                                              width: 10,
                                            ),
                                            Text(
                                              PriceTools.getCurrencyFormatted(
                                                      () {
                                                    final basePrice =
                                                        selectedProduct.price ??
                                                            num.tryParse(stateUI
                                                                    .product
                                                                    .displayPrice ??
                                                                '0') ??
                                                            0;
                                                    final extrasPrice =
                                                        selectedProduct.extras
                                                            .fold<num>(
                                                                0,
                                                                (sum, extra) =>
                                                                    sum +
                                                                    (extra.price ??
                                                                        0));
                                                    return (basePrice +
                                                            extrasPrice) *
                                                        (selectedProduct
                                                                .quantity ??
                                                            1);
                                                  }(), currencyRate,
                                                      currency: currency) ??
                                                  '',
                                              style: TextStyle(
                                                  color: theme
                                                      .colorScheme.secondary,
                                                  fontSize: 14),
                                            ),
                                          ],
                                        ],
                                      ),
                                      // Display extras
                                      if (selectedProduct
                                          .extras.isNotEmpty) ...[
                                        const SizedBox(width: 10),
                                        Wrap(
                                          spacing: 5,
                                          children: selectedProduct.extras
                                              .map((extra) {
                                            return ChooseExtraCard(
                                              extra: extra,
                                              fromCart: true,
                                            );
                                          }).toList(),
                                        ),
                                      ],
                                    ],
                                  );
                                },
                              ),
                            if (stateUI.cartItemMetaData?.selectedComponents
                                    ?.isNotEmpty ??
                                false)
                              Services()
                                  .widget
                                  .renderSelectedComponentsCartItem(
                                      context,
                                      stateUI.cartItemMetaData
                                          ?.selectedComponents),
                            if (stateUI.isPWGiftCardProduct)
                              Services().widget.renderPWGiftCardInfoCartItem(
                                    stateUI.cartItemMetaData?.pwGiftCardInfo,
                                    quantity: stateUI.quantity,
                                    price: stateUI
                                        .cartItemMetaData?.variation?.price,
                                  ),
                            // if (stateUI.showQuantity)
                            //   QuantitySelection(
                            //     enabled: stateUI.inStock &&
                            //         stateUI.onChangeQuantity != null,
                            //     width: 60,
                            //     height: 32,
                            //     color: Theme.of(context).colorScheme.secondary,
                            //     limitSelectQuantity: stateUI.isOnBackorder
                            //         ? kCartDetail['maxAllowQuantity']
                            //         : stateUI.limitQuantity,
                            //     value: stateUI.quantity,
                            //     onChanged: stateUI.onChangeQuantity,
                            //     style: QuantitySelectionStyle.normal,
                            //   ),
                            if (stateUI.product.store != null &&
                                (stateUI.product.store?.name != null &&
                                    stateUI.product.store!.name!
                                        .trim()
                                        .isNotEmpty))
                              const SizedBox(height: 10),
                            if (!stateUI.inStock || stateUI.isOnBackorder)
                              const SizedBox(height: 5),
                            if (stateUI.isOnBackorder)
                              Text(S.of(context).backOrder,
                                  style: TextStyle(
                                    color: kStockColor.backorder,
                                  )),
                            if (!stateUI.isOnBackorder && !stateUI.inStock)
                              Text(
                                S.of(context).outOfStock,
                                style: const TextStyle(color: Colors.red),
                              ),
                            if (!stateUI.isOnBackorder &&
                                stateUI.inStock &&
                                stateUI.quantity != null &&
                                stateUI.quantity! > stateUI.limitQuantity)
                              Text(
                                S.of(context).quantityProductExceedInStock,
                                style: const TextStyle(color: Colors.red),
                              ),
                            if (stateUI.showStoreName)
                              Padding(
                                padding: const EdgeInsets.only(top: 5.0),
                                child: Text(
                                  stateUI.product.store?.name ?? '',
                                  style: TextStyle(
                                      color: theme.colorScheme.secondary,
                                      fontSize: 12),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 16.0),
          ],
        ),
        if (stateUI.enableBottomDivider == true)
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 10.0),
            child: Divider(color: kGrey200, height: 1),
          ),
      ],
    );
  }

  bool _hasQuantityDiscount() {
    final selectedProducts =
        UserBox().selectedSizeAndColorAndExtras[stateUI.product.id.toString()];

    if (selectedProducts == null || selectedProducts.isEmpty) {
      return false;
    }

    final totalQuantity = selectedProducts.fold<int>(
      0,
      (previousValue, element) => previousValue + (element.quantity ?? 0),
    );

    return stateUI.product.minQuantitySaleNumber != null &&
        stateUI.product.quantitySale != null &&
        stateUI.product.quantitySale! > 0 &&
        totalQuantity >= (stateUI.product.minQuantitySaleNumber ?? 0);
  }

  String _getDiscountText(BuildContext context) {
    if (stateUI.product.isQuantitySalePercentage == true) {
      return '${stateUI.product.quantitySale}% ${S.of(context).off}';
    } else {
      return S.of(context).discountApplied;
    }
  }
}
