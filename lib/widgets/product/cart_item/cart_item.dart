import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../common/config.dart';
import '../../../common/tools.dart';
import '../../../data/boxes.dart';
import '../../../generated/l10n.dart';
import '../../../models/cart/cart_base.dart';
import '../../../models/cart/cart_item_meta_data.dart';
import '../../../models/index.dart' show AppModel, Product;
import '../../../services/index.dart';
import '../action_button_mixin.dart';
import 'cart_item_state_ui.dart';
import 'layouts/cart_item_normal_widget.dart';
import 'layouts/cart_item_short_type_widget.dart';
import 'layouts/cart_item_style01_widget.dart';
import 'layouts/cart_item_web_widget.dart';

class ShoppingCartRow extends StatelessWidget with ActionButtonMixin {
  const ShoppingCartRow({
    required this.product,
    required this.quantity,
    this.onRemove,
    this.onChangeQuantity,
    this.cartItemMetaData,
    this.enableTopDivider,
    required this.setState,
    this.enableBottomDivider = true,
    this.showStoreName = true,
    this.enabledTextBoxQuantity = true,
    this.fromCheckout = false,
    this.cartStyle = CartStyle.normal,
  });

  final bool? enableTopDivider;
  final bool enableBottomDivider;
  final bool enabledTextBoxQuantity;
  final Product? product;
  final CartItemMetaData? cartItemMetaData;
  final int? quantity;
  final bool Function(int value)? onChangeQuantity;
  final VoidCallback? onRemove;
  final Function setState;
  final bool showStoreName;
  final CartStyle cartStyle;
  final bool fromCheckout;

  void _onRemoveItem(BuildContext context) {
    showDialog(
      context: context,
      useRootNavigator: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Text(S.of(context).confirmRemoveProductInCart),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(S.of(context).keep),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onRemove?.call();
              },
              child: Text(
                S.of(context).remove,
                style: const TextStyle(
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    var currency = Provider.of<AppModel>(context).currency;
    final currencyRate = Provider.of<AppModel>(context).currencyRate;

    return Selector<CartModel, Product?>(
      selector: (_, cartModel) => cartModel.item[product?.id],
      builder: (context, product, __) {
        if (product == null) {
          return const SizedBox();
        }

        final price = Services().widget.getPriceItemInCart(
            product, cartItemMetaData, currencyRate, currency);

        final imageFeature =
            (cartItemMetaData?.variation?.imageFeature?.isNotEmpty ?? false)
                ? cartItemMetaData?.variation!.imageFeature
                : product.imageFeature;
        var maxQuantity = kCartDetail['maxAllowQuantity'] ?? 100;
        var totalQuantity = cartItemMetaData?.variation != null
            ? (cartItemMetaData?.variation!.stockQuantity ?? maxQuantity)
            : (product.stockQuantity ?? maxQuantity);
        var limitQuantity =
            totalQuantity > maxQuantity ? maxQuantity : totalQuantity;
        final inStock = (cartItemMetaData?.variation != null
                ? cartItemMetaData?.variation!.inStock
                : product.inStock) ??
            false;
        final isOnBackorder = cartItemMetaData?.variation != null
            ? cartItemMetaData?.variation?.backordersAllowed ?? false
            : product.backordersAllowed;
        final selectedProducts =
            UserBox().selectedSizeAndColorAndExtras[product.id.toString()];

        final isPriceSizeNull = selectedProducts?.lastOrNull?.price == null;

        final totalProductQuantity = selectedProducts != null
            ? selectedProducts.fold<int>(
                0,
                (previousValue, element) =>
                    previousValue + (element.quantity ?? 0),
              )
            : 0;

        final totalProductPrice = selectedProducts != null
            ? selectedProducts.fold<num>(
                0,
                (previousValue, element) =>
                    previousValue +
                    ((element.price ?? 0) * (element.quantity ?? 1)),
              )
            : 0;

        // Apply quantity discount to size-based pricing
        final discountedTotalPrice =
            !isPriceSizeNull && totalProductQuantity > 0
                ? PriceTools.calculateQuantityDiscountPrice(
                    basePrice: totalProductPrice / totalProductQuantity,
                    quantity: totalProductQuantity,
                    minQuantitySaleNumber: product.minQuantitySaleNumber,
                    quantitySale: product.quantitySale,
                    isQuantitySalePercentage: product.isQuantitySalePercentage,
                  )
                : totalProductPrice;

        final formattedTotalProductPrice = PriceTools.getCurrencyFormatted(
            discountedTotalPrice, currencyRate,
            currency: currency);

        final priceWithQuantity = (isPriceSizeNull
                ? Services().widget.getPriceItemInCart(
                      product,
                      cartItemMetaData,
                      currencyRate,
                      currency,
                      quantity: quantity ?? 1,
                    )
                : formattedTotalProductPrice)
            ?.toString();

        final stateUI = CartItemStateUI(
          enableBottomDivider: enableBottomDivider,
          inStock: inStock,
          isOnBackorder: isOnBackorder,
          onTapProduct: onTapProduct,
          product: product,
          showStoreName: showStoreName,
          cartItemMetaData: cartItemMetaData,
          enableTopDivider: enableTopDivider,
          imageFeature: imageFeature,
          limitQuantity: limitQuantity,
          enabledTextBoxQuantity: enabledTextBoxQuantity,
          onChangeQuantity: onChangeQuantity,
          onRemove: onRemove != null
              ? () {
                  // Get the current selectedSizeAndColor map
                  var currentMap = UserBox().selectedSizeAndColorAndExtras;

                  // clear all product map
                  currentMap.remove(product.id.toString());

                  // Update the map in the UserBox
                  UserBox().selectedSizeAndColorAndExtras = currentMap;

                  onRemove!();
                }
              : null,
          price: price,
          priceWithQuantity: priceWithQuantity,
          quantity: quantity,
        );

        // Because this case does not need to
        // use LayoutBuilder, so separate it and use if
        if (cartStyle.isWeb) {
          return CartItemWebWidget(
            stateUI,
            setState: setState,
            fromCheckout: fromCheckout,
          );
        }

        return LayoutBuilder(
          builder: (context, constraints) {
            switch (cartStyle) {
              case CartStyle.short:
                return CartItemShortTypeWidget(
                  stateUI,
                  constraintsCurrent: constraints,
                );
              case CartStyle.style01:
                return CartItemStyle01Widget(
                  stateUI,
                  constraintsCurrent: constraints,
                  setState: setState,
                  fromCheckout: fromCheckout,
                );
              case CartStyle.normal:
              default:
                return CartItemNormalWidget(
                  stateUI,
                  heightImageFeature: constraints.maxWidth * 0.3,
                  widthImageFeature: constraints.maxWidth * 0.25,
                  setState: setState,
                  fromCheckout: fromCheckout,
                );
            }
          },
        );
      },
    );
  }
}
