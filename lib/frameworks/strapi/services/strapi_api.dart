import 'dart:async';
import 'dart:convert';

import '../../../common/config.dart';
import '../../../common/constants.dart';

const _backendURL = 'https://backend.idea2app.tech';
const apiURL = '$_backendURL/api';

class StrapiAPI {
  String? url;

  StrapiAPI(this.url);

  String apiLink(String endPoint) {
    return endPoint;
    // return '$url$endPoint';
  }

  Uri? _getOAuthURL(String requestMethod, String endpoint) {
    return Uri.tryParse('$apiURL$endpoint');
    // return Uri.tryParse(url! + endpoint);
  }

  Future<dynamic> getAsync(String endPoint,
      {bool vendorFilter = true,
      bool sort = true,
      int? populateLevel,
      bool ascSort = false}) async {
    try {
      final vendorIdFilter = vendorFilter
          ? '${endPoint.endsWith('?') ? '' : (endPoint.contains('?') ? '&' : '?')}filters[vendor][business_name]=$vendorBusinessName'
          : '';

      final createdAtSortIfNotHaveCreatedAt =
          sort && !endPoint.contains('sort');

      final sortFilter = createdAtSortIfNotHaveCreatedAt
          ? '&sort[0]=createdAt:${ascSort ? 'asc' : 'desc'}'
          : '';

      final populate =
          populateLevel != null ? '&pLevel=$populateLevel' : '&pLevel';

      final filteredVendorEndpoint =
          '$endPoint$vendorIdFilter$sortFilter$populate';

      final url = _getOAuthURL('GET', filteredVendorEndpoint)!;

      printLog(
          "[strapiAPI][${DateTime.now().toString().split(' ').last}] getAsync START [endPoint:$url]");

      final response = await httpGet(
        url,
      );

      final decodedResponse = json.decode(response.body);

      final data = decodedResponse.runtimeType == List
          ? decodedResponse
          : decodedResponse['data'];

      return data;
    } catch (e) {
      printError(e);
    }
  }
}
